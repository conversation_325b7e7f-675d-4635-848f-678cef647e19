import type { Metadata } from "next"
import React from "react"

import Foot<PERSON> from "../components/layout/footer"

import NewHeader from "../components/layout/new-heder"
import ScrollToTop from "../components/scroll-to-top"
import "./globals.css"

export const metadata: Metadata = {
  title:
    "Prism Try-On | Virtual Try-On That Sells - AI-Powered Fashion Technology",
  description:
    'Revolutionary AI-powered virtual try-on solution that adds a simple "Try On" button to your clothing product images. Boost online sales by 40% and reduce returns by 60% without changing your existing website design.',
  keywords:
    "virtual try-on, AI fashion, e-commerce, online shopping, fashion technology, virtual fitting room, clothing try-on, fashion AI, retail technology",
  authors: [{ name: "Prism Try-On" }],
  creator: "Prism Try-On",
  publisher: "Prism Try-On",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://prismtryon.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Prism Try-On | Virtual Try-On That Sells",
    description:
      "Revolutionary AI-powered virtual try-on solution that boosts online sales by 40% and reduces returns by 60%.",
    url: "https://prismtryon.com",
    siteName: "Prism Try-On",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Prism Try-On - Virtual Try-On Technology",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Prism Try-On | Virtual Try-On That Sells",
    description:
      "Revolutionary AI-powered virtual try-on solution that boosts online sales by 40% and reduces returns by 60%.",
    images: ["/images/og-image.jpg"],
    creator: "@prismtryon",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        <link rel='preconnect' href='https://fonts.googleapis.com' />
        <link
          rel='preconnect'
          href='https://fonts.gstatic.com'
          crossOrigin='anonymous'
        />
        <link rel='icon' href='/favicon.ico' />
        <link
          rel='apple-touch-icon'
          sizes='180x180'
          href='/apple-touch-icon.png'
        />
        <link
          rel='icon'
          type='image/png'
          sizes='32x32'
          href='/favicon-32x32.png'
        />
        <link
          rel='icon'
          type='image/png'
          sizes='16x16'
          href='/favicon-16x16.png'
        />
        <link rel='manifest' href='/site.webmanifest' />
        <script
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              name: "Prism Try-On",
              description:
                "AI-powered virtual try-on solution for fashion e-commerce",
              url: "https://prismtryon.com",
              applicationCategory: "BusinessApplication",
              operatingSystem: "Web",
              offers: {
                "@type": "Offer",
                price: "24",
                priceCurrency: "USD",
              },
            }),
          }}
        />
      </head>
      <body className='antialiased'>
        {/* {children} */}
        {/* ---------------------Header Starts-----------------  */}
        <NewHeader />
        {/* ---------------------Header Ends-------------------  */}
        {children}
        {/* ---------------------Footer Starts-----------------  */}
        <Footer />
        {/* ---------------------Footer Ends-----------------  */}
        <ScrollToTop />
      </body>
    </html>
  )
}
