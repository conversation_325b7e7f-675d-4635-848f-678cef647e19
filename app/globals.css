/* @import "tailwindcss"; */

@import url("https://fonts.googleapis.com/css2?family=Afacad:ital,wght@0,400..700;1,400..700&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap");

@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@font-face {
  font-family: "Monument Extended";
  src: url("/fonts/PPMonumentExtended-Black.otf") format("opentype");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Monument Extended";
  src: url("/fonts/MonumentExtended-Ultrabold.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: "Monument Extended";
  /* src: url("/fonts/PPMonumentExtended-Regular.otf") format("opentype"); */
  src: url("/fonts/MonumentExtended-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 0%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 86%;
    --input: 0 0% 86%;
    --ring: 0 0% 0%;
    --radius: 10px;
    --chart-1: 262 83% 58%;
    --chart-2: 328 86% 70%;
    --chart-3: 221 83% 53%;
    --chart-4: 142 76% 36%;
    --chart-5: 47 96% 89%;

    /* Brand Colors for Prism Try-On */

    --brand-blue: 250 93% 71%; /* #6874FB */
    --brand-purple: 270 91% 56%; /* #9522F9 */
    --brand-magenta: 315 91% 50%; /* #E519DD */
    --brand-pink: 330 93% 59%; /* #FC32B2 */
    --brand-coral: 4 94% 71%; /* #FB6C6E */
    --text-muted: 0 0% 43%; /* #6D6D6D */
    --text-light: 0 0% 65%; /* #A7A7A7 */
    --text-dark: 0 0% 9%; /* #171717 */
    --surface: 0 0% 96%; /* #F5F5F5 */
    --border-light: 150 3% 87%; /* #DDDFDE */
    --stats-text: 0 0% 69%; /* #B1B1B1 */
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 10%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 83%;
  }
  .theme {
    --animate-orbit: orbit calc(var(--duration) * 1s) linear infinite;
    --animate-marquee: marquee var(--duration) infinite linear;
    --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background font-afacad text-foreground;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer utilities {
  /* .font-monument {
    font-family: "Monument Extended", "Arial Black", sans-serif;
  }

  .font-afacad {
    font-family: "Afacad", sans-serif;
  }

  .font-inter {
    font-family: "Inter", sans-serif;
  } */

  .gradient-text {
    background: linear-gradient(
      135deg,
      hsl(var(--brand-blue)),
      hsl(var(--brand-purple)),
      hsl(var(--brand-magenta)),
      hsl(var(--brand-pink)),
      hsl(var(--brand-coral))
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-bg {
    background: linear-gradient(
      135deg,
      hsl(var(--brand-blue)),
      hsl(var(--brand-purple)),
      hsl(var(--brand-magenta)),
      hsl(var(--brand-pink)),
      hsl(var(--brand-coral))
    );
  }

  .gradient-border {
    position: relative;
    background: white;
    border-radius: 32px;
  }

  .gradient-border::before {
    content: "";
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(
      135deg,
      hsl(var(--brand-blue)),
      hsl(var(--brand-purple)),
      hsl(var(--brand-magenta)),
      hsl(var(--brand-pink)),
      hsl(var(--brand-coral))
    );
    border-radius: inherit;
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
  }

  .text-hero {
    @apply font-monument text-5xl font-normal leading-tight tracking-tight md:text-6xl lg:text-7xl xl:text-8xl;
  }

  .text-section-title {
    @apply font-monument text-2xl font-normal leading-tight tracking-tight md:text-4xl lg:text-5xl xl:text-[56px];
  }

  .text-body {
    @apply font-afacad text-sm font-normal leading-relaxed md:text-lg;
  }

  .text-body-small {
    @apply font-afacad text-base font-normal leading-relaxed;
  }

  .text-caption {
    @apply font-afacad text-sm font-medium;
  }

  .text-stats {
    @apply font-inter text-3xl font-semibold md:text-4xl lg:text-5xl;
  }

  .text-faq-title {
    @apply font-monument text-6xl font-normal leading-tight md:text-7xl lg:text-8xl;
  }

  .text-price {
    @apply font-monument text-4xl font-normal md:text-5xl;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-primary/5;
}

::-webkit-scrollbar-thumb {
  @apply rounded bg-primary/15;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-foreground;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Remove GSAP animations - using framer-motion only when needed */
.animate-fade-in-up {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in {
  opacity: 1;
}

/* Custom button hover effects */
.btn-gradient-border {
  position: relative;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 32px;
  background-clip: padding-box;
}

.btn-gradient-border::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  margin: -1px;
  border-radius: inherit;
  background: linear-gradient(
    135deg,
    hsl(var(--brand-blue)),
    hsl(var(--brand-purple)),
    hsl(var(--brand-magenta)),
    hsl(var(--brand-pink)),
    hsl(var(--brand-coral))
  );
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
