"use client"

// Import sections
import CTASection from "@/components/sections/cta"
import FAQSection from "@/components/sections/faq"
import FeaturesSection from "@/components/sections/features"
import HeroSection from "@/components/sections/hero-new"
import HowItWorksSection from "@/components/sections/how-it-works"
import ImpactSection from "@/components/sections/impact"
import PricingSection from "@/components/sections/pricing"
import TestimonialsSection from "@/components/sections/testimonials"
import UseCasesSection from "@/components/sections/use-cases"

export default function VirtualTryOnLanding() {
  // useEffect(() => {
  //   // Initialize Lenis smooth scrolling
  //   const initSmoothScrolling = async () => {
  //     const Lenis = (await import('@studio-freight/lenis')).default

  //     const lenis = new Lenis({
  //       duration: 1.2,
  //       easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
  //       touchMultiplier: 2,
  //       infinite: false,
  //     })

  //     function raf(time: number) {
  //       lenis.raf(time)
  //       requestAnimationFrame(raf)
  //     }

  //     requestAnimationFrame(raf)

  //     // Handle anchor links
  //     document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  //       anchor.addEventListener('click', (e: Event) => {
  //         e.preventDefault()
  //         const href = (anchor as HTMLAnchorElement).getAttribute('href')
  //         const target = href ? document.querySelector(href) : null
  //         if (target) {
  //           lenis.scrollTo(target as HTMLElement)
  //         }
  //       })
  //     })

  //     return () => {
  //       lenis.destroy()
  //     }
  //   }

  //   initSmoothScrolling()
  // }, [])

  return (
    <main className='min-h-screen bg-white'>
      <HeroSection />
      <HowItWorksSection />
      <FeaturesSection />
      <UseCasesSection />
      <ImpactSection />
      <PricingSection />
      <TestimonialsSection />
      <FAQSection />
      <CTASection />
    </main>
  )
}
