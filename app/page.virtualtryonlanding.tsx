'use client'

import Header from '@/components/layout/header'
import HeroSection from '@/components/sections/hero'
import HowItWorksSection from '@/components/sections/how-it-works'
import FeaturesSection from '@/components/sections/features'
import UseCasesSection from '@/components/sections/use-cases'
import ImpactSection from '@/components/sections/impact'
import PricingSection from '@/components/sections/pricing'
import TestimonialsSection from '@/components/sections/testimonials'
import FAQSection from '@/components/sections/faq'
import CTASection from '@/components/sections/cta'
import Footer from '@/components/layout/footer'

export default function VirtualTryOnLanding() {
  return (
    <main className="min-h-screen bg-white">
      <Header />
      <HeroSection />
      <HowItWorksSection />
      <FeaturesSection />
      <UseCasesSection />
      <ImpactSection />
      <PricingSection />
      <TestimonialsSection />
      <FAQSection />
      <CTASection />
      <Footer />
    </main>
  )
}