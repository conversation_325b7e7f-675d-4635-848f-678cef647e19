export interface UseCase {
  id: string
  name: string
  icon: string
  title: string
  description: string
  features: string[]
  image: string
}

export const USE_CASES: UseCase[] = [
  {
    id: "ecommerce",
    name: "E-commerce & Retail",
    icon: "/images/use-cases/bag.svg",
    title: "E-commerce & Retail",
    description:
      "Transform online shopping with virtual try-on technology that boosts sales and reduces returns.",
    features: [
      "Boost online sales with virtual try-ons",
      "Reduce return rates significantly",
      "Enhance customer shopping confidence",
      "Integrate with existing e-commerce platforms",
    ],
    image: "/images/dashboard-mockup.svg",
  },
  {
    id: "marketing",
    name: "Marketing & Advertising",
    icon: "/images/use-cases/marketing.svg",

    title: "Marketing & Advertising",
    description:
      "Create engaging campaigns with interactive virtual try-on experiences that capture customer attention.",
    features: [
      "Create engaging social media campaigns",
      "Interactive advertising experiences",
      "Influencer collaboration tools",
      "Brand awareness through virtual experiences",
    ],
    image: "/images/dashboard-mockup.svg",
  },
  {
    id: "software",
    name: "Consumer Software Integration",
    icon: "/images/use-cases/settings.svg",
    title: "Consumer Software Integration",
    description:
      "Seamlessly integrate virtual try-on capabilities into your existing applications and platforms.",
    features: [
      "Add try-on features to shopping apps",
      "Enhance virtual styling platforms",
      "Build interactive digital showrooms",
      "Integrate seamlessly with fashion design tools",
    ],
    image: "/images/dashboard-mockup.svg",
  },
]
