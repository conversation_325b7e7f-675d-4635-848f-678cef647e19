import Image from "next/image"

// Platform icons using custom SVG files
export const PlatformIcons = {
  shopify: () => (
    <Image
      src='/Icons/shopify.svg'
      alt='Shopify'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  wordpress: () => (
    <Image
      src='/Icons/wordpress.svg'
      alt='WordPress'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  webflow: () => (
    <Image
      src='/Icons/webflow.svg'
      alt='Webflow'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  magento: () => (
    <Image
      src='/Icons/magento.svg'
      alt='Magento'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  wix: () => (
    <Image
      src='/Icons/wix.svg'
      alt='Wix'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  discord: () => (
    <Image
      src='/Icons/discord.svg'
      alt='Discord'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  bigcommerce: () => (
    <Image
      src='/Icons/bigcommerce.svg'
      alt='BigCommerce'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  squarespace: () => (
    <Image
      src='/Icons/squarespace.svg'
      alt='Squarespace'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  stripe: () => (
    <Image
      src='/Icons/stripe.svg'
      alt='Stripe'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  prestashop: () => (
    <Image
      src='/Icons/prestashop.svg'
      alt='PrestaShop'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
  opencart: () => (
    <Image
      src='/Icons/opencart.svg'
      alt='OpenCart'
      width={48}
      height={48}
      className='drop-shadow-lg transition-transform duration-300 hover:scale-110'
    />
  ),
}

// Icon groups for different orbiting circles - reduced to 5-6 total logos
export const iconGroups = {
  outer: [PlatformIcons.shopify, PlatformIcons.wordpress],
  middle: [PlatformIcons.magento, PlatformIcons.wix, PlatformIcons.stripe],
  inner: [PlatformIcons.webflow],
}
