export const features = [
  {
    title: "Tailor the Try-On Experience to Your Store",
    description:
      "Easily integrate our plugin into your product pages with full branding control to match your store's look and feel.",
    cta: "ADD TO YOUR STORE",
    image: "/features/1.webp",
    position: "right" as const,
  },
  {
    title: "Simple Try-On Process",
    description:
      "Change outfits in three easy steps on our website — no app or editing skills needed. Works right on mobile.",
    cta: "SEE HOW IT WORKS",
    image: "/features/2.webp",
    position: "left" as const,
  },
  {
    title: "Save more with Prism Try-On",
    description:
      "Upload photos of models and clothing to create ready-to-use images, cutting down on photoshoots.",
    cta: "BUY NOW",
    image: "/features/3.webp",
    position: "right" as const,
  },
  {
    title: "Advanced AI Fashion Technology",
    description:
      "Advanced machine learning technology designed to provide accurate, realistic virtual try-on experiences across all body types and clothing categories.",
    cta: "LEARN MORE",
    image: "/features/4.webp",
    position: "left" as const,
  },
]

export type Feature = (typeof features)[0]
