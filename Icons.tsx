import type { SVGProps } from "react"
type IconProps = SVGProps<SVGSVGElement>

export const Icons = {
  arrowLink: (props: IconProps) => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='25'
      height='25'
      viewBox='0 0 25 25'
      fill='none'
      {...props}
    >
      <path
        d='M7.5 17.1992L17.5 7.19922M17.5 7.19922H7.5M17.5 7.19922V17.1992'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  ),
  infinity: (props: IconProps) => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='48'
      height='25'
      viewBox='0 0 48 25'
      fill='none'
      {...props}
    >
      <path
        d='M35.906 0.000261119C34.1629 -0.0112501 32.4383 0.357962 30.8527 1.0821C29.2671 1.80625 27.8588 2.86785 26.726 4.19276L24.9485 6.24026L28.916 10.8228L31.256 8.12276C31.8303 7.45237 32.5439 6.91521 33.3469 6.54865C34.15 6.18209 35.0233 5.99496 35.906 6.00026C36.698 5.98771 37.4845 6.13285 38.2198 6.42723C38.9551 6.72162 39.6245 7.15937 40.189 7.71498C40.7535 8.27059 41.2018 8.93296 41.5078 9.66351C41.8138 10.3941 41.9714 11.1782 41.9714 11.9703C41.9714 12.7623 41.8138 13.5465 41.5078 14.277C41.2018 15.0076 40.7535 15.6699 40.189 16.2255C39.6245 16.7812 38.9551 17.2189 38.2198 17.5133C37.4845 17.8077 36.698 17.9528 35.906 17.9403C35.0261 17.9461 34.1555 17.7598 33.355 17.3946C32.5544 17.0293 31.8433 16.4937 31.271 15.8253C22.5815 5.79176 26.7103 10.5753 21.191 4.18526C20.0568 2.86266 18.6479 1.80312 17.0625 1.08038C15.4772 0.35763 13.7533 -0.0109586 12.011 0.000261119C8.83638 0.000261119 5.79176 1.26138 3.54695 3.50619C1.30214 5.751 0.0410156 8.79562 0.0410156 11.9703C0.0410156 15.1449 1.30214 18.1895 3.54695 20.4343C5.79176 22.6791 8.83638 23.9403 12.011 23.9403C13.7541 23.9518 15.4787 23.5826 17.0643 22.8584C18.6499 22.1343 20.0583 21.0727 21.191 19.7478L22.9685 17.7003L19.001 13.1178L16.661 15.8178C16.0867 16.4881 15.3732 17.0253 14.5701 17.3919C13.7671 17.7584 12.8938 17.9456 12.011 17.9403C11.2191 17.9528 10.4325 17.8077 9.69721 17.5133C8.9619 17.2189 8.2925 16.7812 7.72802 16.2255C7.16353 15.6699 6.71523 15.0076 6.40923 14.277C6.10322 13.5465 5.94563 12.7623 5.94563 11.9703C5.94563 11.1782 6.10322 10.3941 6.40923 9.66351C6.71523 8.93296 7.16353 8.27059 7.72802 7.71498C8.2925 7.15937 8.9619 6.72162 9.69721 6.42723C10.4325 6.13285 11.2191 5.98771 12.011 6.00026C12.8909 5.99444 13.7615 6.18067 14.5621 6.54596C15.3626 6.91125 16.0738 7.44681 16.646 8.11526C25.3355 18.1488 21.2068 13.3653 26.726 19.7553C28.0267 21.3046 29.6972 22.5007 31.5829 23.2329C33.4686 23.9651 35.5085 24.2097 37.5139 23.9441C39.5192 23.6785 41.4252 22.9113 43.0553 21.7134C44.6853 20.5156 45.9868 18.9258 46.8393 17.0913C47.6918 15.2568 48.0676 13.2369 47.9321 11.2186C47.7966 9.20028 47.154 7.24877 46.0639 5.54474C44.9739 3.84071 43.4715 2.43924 41.6959 1.47007C39.9203 0.500899 37.9289 -0.0046359 35.906 0.000261119Z'
        fill='currentColor'
      />
    </svg>
  ),
  check: (props: IconProps) => (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='1em'
      height='1em'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
      {...props}
    >
      <path d='M20 6 9 17l-5-5' />
    </svg>
  ),
}
