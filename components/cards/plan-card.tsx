import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Check } from "lucide-react"
import GradientBorderButton from "../custom/gradient-border-button"

type Props = {
  plan: {
    name: string
    price: number
    period: string
    features: string[]
    popular: boolean
  }
}

const PlanCard = ({ plan }: Props) => {
  return (
    <Card
      className={`relative mx-auto w-full max-w-sm flex-1 rounded-3xl ${
        plan.popular
          ? "border-black bg-black text-white"
          : "gradient-border border-gray-200 bg-white hover:border-gray-300"
      } transition-all duration-300`}
    >
      {plan.popular && (
        <div className='absolute -top-4 left-1/2 -translate-x-1/2 transform'>
          <div className='gradient-bg rounded-full px-4 py-2 text-sm font-semibold text-white'>
            Most Popular
          </div>
        </div>
      )}

      <CardHeader className='space-y-4 py-8 text-start'>
        <h3
          className={`font-afacad text-xl font-semibold ${
            plan.popular ? "text-white" : "text-muted-foreground"
          }`}
        >
          {plan.name}
        </h3>
        <div className=''>
          <span
            className={`text-price font-bold leading-tight tracking-tight ${
              plan.popular ? "text-white" : "text-foreground"
            }`}
          >
            ${plan.price}
          </span>
        </div>
        <p
          className={`font-afacad text-sm ${
            plan.popular ? "text-gray-300" : "text-muted-foreground"
          }`}
        >
          {plan.period}
        </p>
      </CardHeader>

      <CardContent className='min-h-[170px] space-y-4 px-8'>
        {plan.features.map((feature, featureIndex) => (
          <div key={featureIndex} className='flex items-center gap-3'>
            <Check
              className={`h-4 w-4 ${
                plan.popular ? "text-white" : "text-green-500"
              }`}
            />
            <span
              className={`font-afacad text-sm ${
                plan.popular ? "text-gray-300" : "text-muted-foreground"
              }`}
            >
              {feature}
            </span>
          </div>
        ))}
      </CardContent>

      <CardFooter className='p-8'>
        {/* <Button
          variant={plan.popular ? "gradient" : "gradient"}
          size='lg'
          className={`group w-full rounded-3xl ${plan.popular ? "" : ""}`}
        >
          BUY PLAN
          <GradientArrowIcon
            width={20}
            height={20}
            className='ml-2 transition-transform group-hover:translate-x-1'
          />
        </Button> */}
        <GradientBorderButton className='z-[1] w-full'>
          BUY PLAN
        </GradientBorderButton>
      </CardFooter>
    </Card>
  )
}

export default PlanCard
