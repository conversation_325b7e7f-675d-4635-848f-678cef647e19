import { Testimonial } from "@/constants/testimonials"
import Image from "next/image"

interface TestimonialCardProps {
  testimonial: Testimonial
}

export default function TestimonialCard({ testimonial }: TestimonialCardProps) {
  return (
    <div className='mx-4 w-[350px] rounded-2xl p-6 text-center transition-all duration-300 md:w-[450px] md:p-8'>
      {/* Rating */}
      <div className='mb-4 flex justify-center gap-1'>
        {[...Array(testimonial.rating)].map((_, i) => (
          <Image
            key={i}
            src='/Icons/star.svg'
            alt='Star rating'
            width={20}
            height={20}
            className='h-5 w-5'
          />
        ))}
      </div>

      {/* Quote */}
      <blockquote className='text-body-small mb-6 text-center font-afacad leading-relaxed text-foreground'>
        &quot;{testimonial.quote}&quot;
      </blockquote>

      {/* Author */}
      <div className='text-center'>
        <div className='mb-1 font-afacad font-semibold text-foreground'>
          {testimonial.author}
        </div>
        <div className='font-afacad text-sm text-muted-foreground'>
          {testimonial.position}
        </div>
      </div>
    </div>
  )
}
