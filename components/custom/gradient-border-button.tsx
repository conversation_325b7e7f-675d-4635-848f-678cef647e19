import React from "react"
import GradientArrowIcon from "../icons/gradient-arrow-icon"

interface GradientBorderButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  className?: string
  asLink?: boolean
  href?: string
}

const GradientBorderButton: React.FC<GradientBorderButtonProps> = ({
  children,
  className = "",
  asLink = false,
  href,
  ...props
}) => {
  const content = (
    <>
      <span className='font-monument tracking-normal text-primary'>
        {children}
      </span>
      <GradientArrowIcon
        width={36}
        height={36}
        className='ml-[18.42px] size-[32px] transition-transform duration-300 ease-in-out group-hover:rotate-45 md:size-[36px]'
      />
    </>
  )

  const commonClasses = `
    group
    flex
    uppercase 
    items-center
    justify-center
    rounded-[50.39px]
    p-[0.5px] md:p-[0.75px]
    gap-[18.42px]
    border-[0.75px]
    text-xs md:text-sm
    border-transparent
    bg-gradient-to-r
    from-[#62CDFF]
    via-[#7D42A5]
    to-[#E4398A]
    bg-origin-border
    shadow-[0px_11.2px_11.2px_0px_#DFDFDF40]
    hover:shadow-2xl
    hover:shadow-purple-600/30
    transition-all transition-colors
    duration-500
    ${className}
  `

  const innerClasses = `
    flex
    h-full
    w-full
    items-center
    justify-between 
    rounded-[50.39px]
    bg-white
    pl-4 md:pl-6
    cursor-pointer
    py-1 md:py-1.5
    pr-1 md:pr-1.5
    transition-all transition-colors
    duration-500
    ease-in-out
    group-hover:bg-gradient-to-r
    group-hover:from-[#62CDFF]/10
    group-hover:via-[#7D42A5]/10
    group-hover:to-[#E4398A]/10
  `

  if (asLink && href) {
    return (
      <a href={href} className={commonClasses}>
        <div className={innerClasses}>{content}</div>
      </a>
    )
  }

  return (
    <button type='button' className={commonClasses} {...props}>
      <div className={innerClasses}>{content}</div>
    </button>
  )
}

export default GradientBorderButton
