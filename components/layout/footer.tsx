"use client"

import Image from "next/image"
import Link from "next/link"
import { Marquee } from "../ui/marquee"

export default function Footer() {
  const footerLinks = {
    company: [
      { label: "About", href: "/about" },
      { label: "Work", href: "/work" },
      { label: "Service", href: "/service" },
      { label: "Contact", href: "/contact" },
    ],
    social: [
      { label: "LinkedIn", href: "https://linkedin.com/company/prismtryon" },
      { label: "Behance", href: "https://behance.net/prismtryon" },
      { label: "Instagram", href: "https://instagram.com/prismtryon" },
    ],
    legal: [
      { label: "Privacy policy", href: "/privacy" },
      { label: "Terms and conditions", href: "/terms" },
    ],
  }

  return (
    <footer className='relative h-full overflow-hidden bg-black py-0 text-white'>
      {/* Gradient Background Effect - Bottom Right */}
      <div className='absolute right-[10%] min-h-[300px] min-w-[150px] -translate-y-1/2 bg-[#B07CFF] opacity-40 blur-[170px] md:min-h-[600px] md:min-w-[300px]' />

      <div className='relative mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Main Footer Content */}
        <div className='gap- mb-16 flex flex-row items-start justify-between pt-4 lg:items-start lg:gap-20'>
          {/* Logo */}
          <div className='flex-shrink-0'>
            {/* <div className='text-2xl font-bold text-white'>PRISM</div> */}
            <Image
              src='/images/prism-logo.png'
              alt='Prism Logo'
              width={100}
              height={100}
              className='h-auto w-full max-w-[100px]'
            />
          </div>

          {/* Navigation Links */}
          <div className='space-y-6'>
            {/* Company Links */}
            <div className='flex flex-col'>
              {footerLinks.company.map((link) => (
                <Link
                  key={link.label}
                  href={link.href}
                  className={`font-afacad text-lg leading-5 transition-colors duration-200 hover:text-gray-300 ${
                    link.label === "Service" ? "gradient-text" : "text-white"
                  }`}
                >
                  {link.label}
                </Link>
              ))}
            </div>

            <div className='flex flex-col pt-4'>
              {/* Social Links */}
              {footerLinks.social.map((link) => (
                <Link
                  key={link.label}
                  href={link.href}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='font-afacad text-lg leading-5 text-white transition-colors duration-200 hover:text-gray-300'
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className='border-t border-gray-800 pt-8'>
          <div className='flex flex-col items-center justify-between gap-6 md:flex-row'>
            {/* Copyright */}
            <p className='font-afacad text-sm text-gray-400'>
              ©{new Date().getFullYear()} All Rights Reserved
            </p>

            {/* Legal Links */}
            <div className='flex gap-8'>
              {footerLinks.legal.map((link) => (
                <Link
                  key={link.label}
                  href={link.href}
                  className='font-afacad text-sm text-gray-400 transition-colors duration-200 hover:text-gray-300'
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Large Text Background */}
      <div className='mt-16 overflow-hidden'>
        <div className='flex gap-20 opacity-10'>
          <Marquee className='[--duration:20s]'>
            <h3 className='mr-3 whitespace-nowrap font-monument text-6xl font-bold leading-none text-white md:mr-5 md:text-7xl lg:mr-16 lg:text-8xl'>
              PRISMSCALE
            </h3>
          </Marquee>

          {/* <h3 className='whitespace-nowrap font-monument text-8xl font-normal leading-none text-white md:text-9xl lg:text-[12rem]'>
            PRISMSCALE
          </h3> */}
        </div>
      </div>
    </footer>
  )
}
