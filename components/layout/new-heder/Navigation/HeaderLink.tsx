"use client"
import { motion } from "framer-motion"
import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"
import React, { Suspense, useEffect, useState } from "react"

export type HeaderItem = {
  label: string
  href: string
}

const OFFSET = 80 // Adjust this value based on your fixed header height

// Hook to manage the active link and apply offset
const useActiveLink = (setActiveLink: (link: string) => void) => {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const updateActiveLink = () => {
      const fullPath = window.location.hash
        ? `${pathname}${window.location.hash}`
        : pathname
      setActiveLink(fullPath)
    }

    const handleScrollOffset = () => {
      if (window.location.hash) {
        const id = window.location.hash.substring(1)
        const element = document.getElementById(id)
        if (element) {
          setTimeout(() => {
            const elementPosition =
              element.getBoundingClientRect().top + window.scrollY
            window.scrollTo({
              top: elementPosition - OFFSET,
              behavior: "smooth",
            })
          }, 0)
        }
      }
    }

    updateActiveLink()
    handleScrollOffset()

    window.addEventListener("hashchange", updateActiveLink)
    window.addEventListener("hashchange", handleScrollOffset)

    return () => {
      window.removeEventListener("hashchange", updateActiveLink)
      window.removeEventListener("hashchange", handleScrollOffset)
    }
  }, [pathname, searchParams, setActiveLink])
}

// HeaderLink component
const HeaderLinkContent: React.FC<{ item: HeaderItem }> = ({ item }) => {
  const [activeLink, setActiveLink] = useState("")

  useActiveLink(setActiveLink)

  return (
    <li>
      <Link
        href={item.href}
        className={`relative px-4 py-2 font-medium transition-colors duration-300 ${
          activeLink === item.href ? "text-black dark:text-white" : null
        }`}
      >
        {activeLink === item.href && (
          <motion.div
            className='shadow-header_shadow absolute inset-0 rounded-[2rem] bg-white'
            layoutId='active-pill'
          ></motion.div>
        )}
        <span className='relative z-10 dark:mix-blend-exclusion'>
          {item.label}
        </span>
      </Link>
    </li>
  )
}

// Wrap in Suspense
const HeaderLink: React.FC<{ item: HeaderItem }> = ({ item }) => (
  <Suspense fallback={null}>
    <HeaderLinkContent item={item} />
  </Suspense>
)

export default HeaderLink
