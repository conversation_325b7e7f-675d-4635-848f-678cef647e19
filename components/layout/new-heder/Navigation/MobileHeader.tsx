import Link from "next/link"
import React from "react"
import { HeaderItem } from "./Menudata"

const MobileHeader: React.FC<{ item: HeaderItem }> = ({ item }) => {
  return (
    <>
      <Link
        href={item.href}
        className='rounded-md text-base font-medium text-black hover:text-opacity-50 dark:text-white dark:hover:text-opacity-50'
      >
        <li className={`w-full rounded-md p-2 px-4`}>{item.label}</li>
      </Link>
    </>
  )
}

export default MobileHeader
