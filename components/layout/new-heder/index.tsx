"use client"

import Image from "next/image"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "../../ui/button"
import HeaderLink from "./Navigation/HeaderLink"
import { headerData } from "./Navigation/Menudata"
import MobileHeader from "./Navigation/MobileHeader"

const NewHeader = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sticky, setSticky] = useState(false)
  const pathname = usePathname()

  const handleScroll = () => {
    setSticky(window.scrollY >= 80)
  }

  useEffect(() => {
    window.addEventListener("scroll", handleScroll)

    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [pathname])

  return (
    <>
      <header className={`fixed top-0 z-50 w-full`}>
        <div className='container p-3'>
          <nav
            className={`flex w-full items-center justify-between rounded-full border border-[#ECECEC80] px-2 py-1 ${
              sticky
                ? "dark:bg-dark_black rounded-full bg-white/80 shadow-sm"
                : null
            } `}
          >
            <div className='flex w-full flex-[1] items-center'>
              <Image
                className='ml-3 w-10 md:ml-5 md:w-12'
                width={100}
                height={50}
                src={"/icons/logo.svg"}
                alt='logo'
              />
            </div>
            <div className='bg-dark_black/5 hidden w-full flex-[2] items-center justify-center rounded-3xl px-1 py-3 dark:bg-white/5 lg:flex'>
              <ul className='flex gap-0 2xl:gap-1.5'>
                {headerData.map((item, index) => (
                  <HeaderLink key={index} item={item} />
                ))}
              </ul>
            </div>
            <div className='flex w-full flex-[1] items-center justify-end gap-1 xl:gap-4'>
              <Button
                variant='secondary'
                className='gradient-text hidden min-w-[160px] rounded-full border border-gray-100 py-5 font-afacad text-[16px] md:flex'
              >
                Get Prism Try-On
              </Button>
              {/* ---------------------Light/Dark Mode button-------------------- */}

              <div className='mr-3 hidden max-lg:flex'>
                <button onClick={() => setSidebarOpen(!sidebarOpen)}>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    width='24'
                    height='25'
                    fill='none'
                    viewBox='0 0 24 25'
                  >
                    <path
                      stroke='url(#paint0_linear_258_456)'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M3 12.695h18m-18 6h18m-18-12h18'
                    ></path>
                    <defs>
                      <linearGradient
                        id='paint0_linear_258_456'
                        x1='12'
                        x2='12'
                        y1='6.694'
                        y2='18.695'
                        gradientUnits='userSpaceOnUse'
                      >
                        <stop stopColor='#D421D7'></stop>
                        <stop offset='1' stopColor='#FB785F'></stop>
                      </linearGradient>
                    </defs>
                  </svg>
                </button>
              </div>
            </div>
          </nav>
        </div>

        {/* ------------------------- Mobile sidebar starts ------------------------- */}
        {sidebarOpen && (
          <div
            className='fixed left-0 top-0 z-40 h-full w-full bg-black/50'
            onClick={() => setSidebarOpen(false)}
          />
        )}
        <div
          className={`dark:bg-dark_black fixed right-0 top-0 h-full w-full max-w-xs transform bg-white shadow-lg transition-transform duration-300 lg:hidden ${
            sidebarOpen ? "translate-x-0" : "translate-x-full"
          } z-50`}
        >
          <div className='flex items-center justify-between p-4'>
            <h2 className='text-lg font-bold'>Menu</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              aria-label='Close mobile menu'
            >
              <svg
                xmlns='http://www.w3.org/2000/svg'
                width='24'
                height='24'
                viewBox='0 0 24 24'
              >
                <path
                  fill='none'
                  stroke='currentColor'
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M6 18L18 6M6 6l12 12'
                />
              </svg>
            </button>
          </div>
          <div className='p-4'>
            <ul className='flex flex-col'>
              {headerData.map((item, index) => (
                <MobileHeader key={index} item={item} />
              ))}
            </ul>
            <Button
              variant='secondary'
              className='gradient-text mt-5 w-full min-w-[160px] rounded-full border border-gray-100 py-5 font-afacad text-[16px]'
            >
              Get Prism Try-On
            </Button>
          </div>
        </div>
      </header>
    </>
  )
}

export default NewHeader
