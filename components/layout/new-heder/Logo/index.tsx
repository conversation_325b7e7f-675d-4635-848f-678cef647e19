import Link from "next/link"
import React from "react"

interface HeaderProps {}
const Logo: React.FC<HeaderProps> = () => {
  return (
    <Link href='/'>
      <span
        className='font-monument-extended bg-clip-text text-lg uppercase text-transparent md:text-xl lg:text-2xl'
        style={{
          backgroundImage:
            "linear-gradient(186deg, #CFC704 6%, #B32468 57%, #542989 100%);",
          WebkitBackgroundClip: "text",
          backgroundClip: "text",
          WebkitTextFillColor: "transparent",
        }}
      >
        PRISM QR
      </span>
    </Link>
  )
}

export default Logo
