"use client"
import { Menu, X } from "lucide-react"
import { useEffect, useState } from "react"

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navItems = [
    { label: "Features", href: "#features" },
    { label: "Use Cases", href: "#use-cases" },
    { label: "Pricing", href: "#pricing" },
  ]

  return (
    <header
      className={`w-full. fixed left-0 right-0 top-0 z-50 flex h-16 items-center justify-center transition-all duration-300 ${
        isScrolled
          ? "bg-background shadow-sm backdrop-blur-md"
          : "bg-transparent"
      }`}
    >
      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex items-center justify-between'>
          {/* Logo - Left */}
          <div className='flex-[1] flex-shrink-0'>
            <span className='text-xl font-bold'>Logo</span>
          </div>

          {/* Desktop Navigation - Center */}
          <nav className='hidden flex-[2] md:absolute md:left-1/2 md:flex md:-translate-x-1/2 md:transform'>
            <ul className='flex space-x-12'>
              {navItems.map((item) => (
                <li key={item.label}>
                  <a
                    href={item.href}
                    className='font-afacad text-lg font-medium text-foreground transition-colors duration-200 hover:text-muted-foreground'
                  >
                    {item.label}
                  </a>
                </li>
              ))}
            </ul>
          </nav>

          {/* CTA Button - Right */}
          <div className='hidden flex-[1] md:block'>
            <button
              type='button'
              className='rounded-full border border-solid border-gray-400 !bg-[#B6B6B6] font-afacad'
            >
              Get Prism Try-On
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className='rounded-md p-2 text-foreground transition-colors hover:bg-muted md:hidden'
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label='Toggle mobile menu'
          >
            {isMobileMenuOpen ? (
              <X className='h-6 w-6' />
            ) : (
              <Menu className='h-6 w-6' />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className='absolute left-0 right-0 top-full border-t border-border bg-white/95 shadow-lg backdrop-blur-md md:hidden'>
            <nav className='space-y-4 px-4 py-6'>
              {navItems.map((item) => (
                <a
                  key={item.label}
                  href={item.href}
                  className='block font-medium text-foreground transition-colors duration-200 hover:text-muted-foreground'
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.label}
                </a>
              ))}
              {/* <div className='pt-4'>
                <Button variant='gradient' size='lg' className='w-full'>
                  Get Prism Try-On
                </Button>
              </div> */}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
