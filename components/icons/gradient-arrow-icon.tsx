import type { SVGProps } from "react"
const GradientArrowIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    fill='none'
    viewBox='0 0 42.398 42.398'
    {...props}
  >
    <defs>
      <linearGradient
        xmlns='http://www.w3.org/2000/svg'
        id='prefix__a'
        x1={55.576}
        x2={10.55}
        y1={8.07}
        y2={38.09}
        gradientUnits='userSpaceOnUse'
      >
        <stop offset={0.06} stopColor='#CFC704' />
        <stop offset={0.568} stopColor='#B32468' />
        <stop offset={1} stopColor='#542989' />
      </linearGradient>
    </defs>
    <g xmlns='http://www.w3.org/2000/svg'>
      <circle cx={21.199} cy={21.2} r={21.199} fill='url(#prefix__a)' />
      <path
        stroke='#fff'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='m16.2 26.2 10-10m0 0h-10m10 0v10'
      />
    </g>
  </svg>
)
export default GradientArrowIcon
