"use client"

import GradientBorderButton from "../custom/gradient-border-button"

export default function CTASection() {
  return (
    <section className='bg-gradient-to-br from-gray-50 via-white to-gray-50 py-20 md:py-32'>
      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='mx-auto text-center'>
          <h2 className='mb-6 font-afacad text-4xl font-bold leading-tight text-foreground md:text-5xl lg:text-6xl'>
            Turn Browsers into Buyers with{" "}
            <span className='gradient-text'>Prism Try-On</span>
          </h2>

          <p className='text-body mx-auto mb-8 max-w-2xl text-muted-foreground'>
            Join our 100 beta fashion retailers and get 30 days free. Add
            virtual fitting room buttons to your products — no design changes
            needed.
          </p>

          <div className='flex items-center justify-center'>
            {/* <Button variant='gradient' size='xl' className='group rounded-full'>
              CONTACT US
              <GradientArrowIcon
                width={24}
                height={24}
                className='ml-2 transition-transform group-hover:translate-x-1'
              />
            </Button> */}
            <GradientBorderButton className='z-[1]'>
              CONTACT US
            </GradientBorderButton>
          </div>
        </div>
      </div>
    </section>
  )
}
