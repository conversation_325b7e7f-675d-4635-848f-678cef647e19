"use client"
import { AnimatePresence, motion } from "framer-motion"
import Image from "next/image"
import { useEffect, useMemo, useState } from "react"
import GradientBorderButton from "../custom/gradient-border-button"

export default function HeroSection() {
  const models = useMemo(
    () => [
      {
        id: 1,
        src: "/images/models/model1.png",
        alt: "Man virtual try-on model",
      },
      {
        id: 2,
        src: "/images/models/model2.png",
        alt: "Women virtual try-on",
      },
    ],
    [],
  )
  const length = models.length
  const [currentModelIndex, setCurrentModelIndex] = useState(0)
  const animationDuration = 3000

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentModelIndex((prevIndex) => (prevIndex + 1) % length)
    }, animationDuration)
    return () => clearInterval(interval)
  }, [length])

  return (
    <section className='relative flex items-start justify-center overflow-hidden bg-background pt-20 md:min-h-screen md:items-center md:pt-32'>
      {/* Gradient Background Effect */}
      {/* Left Gradient - White toward center */}
      <div className='absolute left-[10%] top-0 block min-h-[200px] min-w-[100px] translate-y-1/2 bg-[#F16B6B] opacity-30 blur-[120px] md:min-h-[400px] md:min-w-[200px]' />
      {/* Right Gradient - Pink/Purple toward center */}
      {/* <div className='absolute right-0 top-0 h-full w-1/2 bg-gradient-to-l from-transparent via-transparent to-[#EB20D1] opacity-15 blur-[120px]' /> */}
      <div className='absolute right-[10%] top-0 block min-h-[200px] min-w-[100px] translate-y-1/2 bg-[#EB20D1] opacity-30 blur-[120px] md:min-h-[400px] md:min-w-[200px]' />

      <div className='container relative mx-auto px-4 pt-5 sm:px-6 md:px-8 md:pt-0'>
        <div className='relative mx-auto max-w-6xl text-center'>
          {/* Background Pattern */}
          <div className='absolute inset-0 flex items-center justify-center opacity-20'>
            <Image
              src='/images/circular-pattern.svg'
              alt=''
              width={1014}
              height={1014}
              className='h-auto w-full max-w-4xl'
              priority
            />
          </div>
          {/* Main Title (no animation) */}
          <div className='text-center align-middle font-monument font-normal leading-[48.74px] md:leading-[78.25px] md:tracking-[-3.51px]'>
            <p className='text-[32.6px] md:text-[81.87px]'>Virtual Try-On</p>
            <p className='flex items-center justify-center gap-10 text-[44.6px] !text-[#D3D3D3] md:gap-20 md:text-[81.87px] md:text-inherit'>
              <span>That</span> <span>Sells</span>
            </p>
          </div>
          {/* Phone Mockups */}
          <div className='relative flex items-center justify-center'>
            {/* 3D Mobile Phone Base */}
            <Image
              src='/images/3d-mobile.png'
              alt='3D mobile phone mockup'
              width={500}
              height={200}
              className='absolute bottom-0 mx-auto object-contain md:bottom-0'
              priority
            />

            <div className='relative aspect-[9/16] h-[300px] w-[300px] md:h-[500px]'>
              {/* Model Images Container */}
              <div className='b absolute left-1/2 top-[-10%] h-full w-[92%] -translate-x-1/2 rounded-lg md:top-[-8%] md:h-[85%]'>
                <AnimatePresence mode='wait'>
                  <motion.div
                    key={models[currentModelIndex].id}
                    initial={{
                      opacity: 0,
                      scale: 0.8,
                      y: 30,
                      rotateY: -15,
                      filter: "blur(4px)",
                    }}
                    animate={{
                      opacity: 1,
                      scale: 1,
                      y: 0,
                      rotateY: 0,
                      filter: "blur(0px)",
                    }}
                    exit={{
                      opacity: 0,
                      scale: 0.8,
                      y: -30,
                      rotateY: 15,
                      filter: "blur(4px)",
                    }}
                    transition={{ duration: 0.8, ease: "backOut" }}
                    className='h-full w-full'
                  >
                    <Image
                      src={models[currentModelIndex].src}
                      alt={models[currentModelIndex].alt}
                      fill
                      className='object-contain md:scale-125'
                      priority={currentModelIndex === 0}
                    />
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
          {/* Subtitle */}
          <p className='mx-auto mb-4 max-w-2xl px-6 pt-5 text-center text-sm text-muted-foreground md:mb-8 md:pt-0 md:text-lg'>
            Revolutionary AI-powered virtual try-on solution that adds a simple
            &apos;Try On&apos; button to your clothing product images. Boost
            online sales by 40% and reduce returns by 60% without changing your
            existing website design.
          </p>
          {/* CTA Button */}
          <div className='flex w-full items-center justify-center'>
            <GradientBorderButton className='z-[1]'>
              Get Prism Try-On
            </GradientBorderButton>
          </div>
        </div>
      </div>
    </section>
  )
}
