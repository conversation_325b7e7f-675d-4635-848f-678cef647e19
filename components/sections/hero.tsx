"use client"
import Image from "next/image"
import GradientBorderButton from "../custom/gradient-border-button"

export default function HeroSection() {
  return (
    <section className='relative flex min-h-screen items-center justify-center overflow-hidden bg-background pt-20 md:pt-36'>
      <div className='container relative mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='relative mx-auto max-w-6xl text-center'>
          {/* Background Pattern */}
          <div className='absolute inset-0 flex items-center justify-center opacity-20'>
            <Image
              src='/images/circular-pattern.svg'
              alt=''
              width={1014}
              height={1014}
              className='h-auto w-full max-w-4xl'
              priority
            />
          </div>
          {/* Main Title */}
          <h1 className='mb-10 font-monument text-4xl font-normal tracking-tight sm:text-6xl md:text-5xl lg:text-6xl'>
            Virtual Try-On
            <br />
            <span className='bg-gradient-to-r from-primary to-primary/75 bg-clip-text text-transparent'>
              That Sells
            </span>
          </h1>

          {/* Phone Mockups */}
          <div className='relative mb-12 flex items-end justify-center gap-4 md:gap-8'>
            <Image
              src='/images/model-man-grey.jpg'
              alt='Man in grey blazer virtual try-on'
              width={1000}
              height={600}
              className='object-contain'
              // sizes='(max-width: 768px) 160px, (max-width: 1024px) 224px, 256px'
            />
            {/* Left Phone */}
            <Image
              src='/images/left-women.png'
              alt='Woman in beige outfit virtual try-on'
              // fill
              width={300}
              height={800}
              className='absolute bottom-16 left-[13%] w-24 object-contain md:w-56'

              // sizes='(max-width: 768px) 128px, (max-width: 1024px) 192px, 224px'
            />
            {/* center */}
            <Image
              src='/images/cropped-man.png'
              alt='Woman in beige outfit virtual try-on'
              width={300}
              height={1000}
              className='absolute bottom-12 w-24 object-contain md:w-48'

              // sizes='(max-width: 768px) 128px, (max-width: 1024px) 192px, 224px'
            />
            {/* right */}
            <Image
              src='/images/right-women.png'
              alt='Woman in beige outfit virtual try-on'
              width={300}
              height={800}
              className='absolute bottom-16 right-[13%] w-24 object-contain md:w-56'

              // sizes='(max-width: 768px) 128px, (max-width: 1024px) 192px, 224px'
            />
          </div>

          {/* Subtitle */}
          <p className='mx-auto mb-8 max-w-2xl text-sm text-muted-foreground md:text-lg'>
            Revolutionary AI-powered virtual try-on solution that adds a simple
            &apos;Try On&apos; button to your clothing product images. Boost
            online sales by 40% and reduce returns by 60% without changing your
            existing website design.
          </p>

          {/* CTA Button */}
          <div className='flex w-full items-center justify-center pt-3 md:pt-7'>
            <GradientBorderButton className='z-[1]'>
              Get Prism Try-On
            </GradientBorderButton>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      {/* <div className='absolute bottom-12 left-1/2 -translate-x-1/2 transform animate-bounce'>
        <div className='flex h-10 w-6 justify-center rounded-full border-2 border-muted'>
          <div className='mt-2 h-3 w-1 animate-pulse rounded-full bg-muted'></div>
        </div>
      </div> */}
    </section>
  )
}
