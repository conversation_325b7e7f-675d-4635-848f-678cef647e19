"use client"

import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Image from "next/image"
import React, { useEffect, useRef, useState } from "react"
import { features } from "../../../constants/features"
import GradientBorderButton from "../../custom/gradient-border-button"

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger)
}

export default function MobileFeaturesSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const sliderRef = useRef<HTMLDivElement>(null)
  const [currentSlide, setCurrentSlide] = useState(0)
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)
  useEffect(() => {
    if (!sectionRef.current) return

    const ctx = gsap.context(() => {
      // Animate section elements
      gsap.fromTo(
        ".feature-title",
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
          },
        },
      )

      gsap.fromTo(
        ".feature-slider",
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 70%",
          },
        },
      )
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  // Handle swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe && currentSlide < features.length - 1) {
      setCurrentSlide(currentSlide + 1)
    }
    if (isRightSwipe && currentSlide > 0) {
      setCurrentSlide(currentSlide - 1)
    }
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  return (
    <section ref={sectionRef} className='px-4 py-16' id='features'>
      <div className='mx-auto'>
        {/* Section Header */}
        <div className='mb-12 space-y-3 px-3 text-center'>
          <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-1.5 text-muted-foreground md:py-2.5'>
            Features
          </p>
          <h2 className='font-monument text-[22px]'>
            The Smartest Way to Try Before You Buy
          </h2>
          <p className='feature-title mx-auto max-w-sm px-3 text-sm font-medium leading-relaxed text-[#3B3B3B]'>
            From immersive product previews to seamless integration, Prism
            Try-On tool is built to boost engagement, sales, and customer Try-On
            tool is built to boost engagement, sales, and customer satisfaction
            — all while fitting effortlessly into your store’s workflow.wers
            about working with us and our approach to digital solutions
          </p>
        </div>

        {/* Feature Slider */}
        <div className='feature-slider relative'>
          <div
            className='overflow-hidden rounded-xl'
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <div
              ref={sliderRef}
              className='flex transition-transform duration-500 ease-out'
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {features.map((feature, index) => (
                <div key={index} className='w-full flex-shrink-0 px-4'>
                  <div className='mx-auto w-full rounded-3xl p-6 px-3'>
                    {/* Image */}
                    <div className='relative mb-8'>
                      <div className='relative transform overflow-hidden rounded-2xl'>
                        <Image
                          src={feature.image}
                          alt={feature.title}
                          width={400}
                          height={300}
                          className='h-auto w-full'
                        />
                      </div>
                    </div>

                    {/* Content */}
                    <div className='space-y-6 text-center'>
                      <h3 className='text-xl font-bold leading-tight tracking-tight text-foreground'>
                        {feature.title}
                      </h3>
                      <p className='text-sm leading-relaxed text-muted-foreground'>
                        {feature.description}
                      </p>
                      <div className='pt-2'>
                        <GradientBorderButton className='w-full text-xs'>
                          {feature.cta}
                        </GradientBorderButton>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation with Arrows and Dot Indicators */}
        <div className='mt-8 flex items-center justify-center space-x-6'>
          {/* Left Arrow */}
          <button
            onClick={() =>
              currentSlide > 0 && setCurrentSlide(currentSlide - 1)
            }
            className={`flex h-8 w-8 items-center justify-center rounded-full transition-all duration-300 ${
              currentSlide === 0
                ? "cursor-not-allowed opacity-30"
                : "opacity-70 hover:opacity-100"
            }`}
            disabled={currentSlide === 0}
            aria-label='Previous feature'
          >
            <svg
              width='16'
              height='16'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
              className='text-gray-600'
            >
              <polyline points='15,18 9,12 15,6'></polyline>
            </svg>
          </button>

          {/* Dot Indicators */}
          <div className='flex items-center space-x-3'>
            {features.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`rounded-full transition-all duration-300 ease-out hover:scale-110 ${
                  index === currentSlide
                    ? "h-3 w-8 bg-gradient-to-r from-[#F16B6B] to-[#EB20D1] shadow-lg shadow-pink-200/40"
                    : "h-2 w-2 bg-gray-300 hover:bg-gray-400"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>

          {/* Right Arrow */}
          <button
            onClick={() =>
              currentSlide < features.length - 1 &&
              setCurrentSlide(currentSlide + 1)
            }
            className={`flex h-8 w-8 items-center justify-center rounded-full transition-all duration-300 ${
              currentSlide === features.length - 1
                ? "cursor-not-allowed opacity-30"
                : "opacity-70 hover:opacity-100"
            }`}
            disabled={currentSlide === features.length - 1}
            aria-label='Next feature'
          >
            <svg
              width='16'
              height='16'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
              className='text-gray-600'
            >
              <polyline points='9,18 15,12 9,6'></polyline>
            </svg>
          </button>
        </div>
      </div>
    </section>
  )
}
