"use client"

import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Image from "next/image"
import { useEffect, useRef } from "react"
import { features } from "../../../constants/features"
import { cn } from "../../../lib/utils"
import GradientBorderButton from "../../custom/gradient-border-button"

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger)
}

export default function DesktopFeaturesSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!sectionRef.current) return

    const ctx = gsap.context(() => {
      // Animate section elements
      gsap.fromTo(
        ".feature-title",
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
          },
        },
      )

      gsap.fromTo(
        ".feature-card",
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 70%",
          },
        },
      )
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section
      ref={sectionRef}
      className='px-2 py-24 md:py-[100px]'
      id='features'
    >
      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Section Header */}
        <div className='mb-16 space-y-6 text-center md:mb-24 md:space-y-8'>
          <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-2.5 text-muted-foreground'>
            Features
          </p>
          <h2 className='text-section-title feature-title'>
            The Smartest Way to Try
            <br />
            Before You Buy
          </h2>
          <p className='text-body feature-title mx-auto max-w-3xl text-primary'>
            From immersive product previews to seamless integration, Prism
            Try-On tool is built to boost engagement, sales, and customer
            satisfaction - all while fitting effortlessly into your store&apos;s
            workflow.
          </p>
        </div>

        {/* Feature Cards */}
        <div className='relative mx-auto max-w-3xl space-y-20 md:space-y-32'>
          {/* center card */}
          <div className='relative mx-auto w-max'>
            <div className='absolute left-[10%] top-0 block min-h-[200px] min-w-[80px] translate-y-1/2 bg-[#F16B6B] opacity-50 blur-[80px]' />

            <div className='absolute right-[10%] top-0 block min-h-[200px] min-w-[80px] translate-y-1/2 bg-[#EB20D1] opacity-50 blur-[80px]' />
            <div className='relative transform overflow-hidden rounded-2xl'>
              <Image
                src={"/features/1.webp"}
                alt='1st'
                width={600}
                height={400}
                className='h-auto w-full md:w-60'
              />
            </div>
          </div>

          {features.map((feature, index) => (
            <div
              key={index}
              className={`feature-card flex flex-col items-start gap-12 md:flex-row lg:gap-20 ${
                feature.position === "left" ? "md:flex-row-reverse" : ""
              }`}
            >
              {/* Content */}
              <div
                className={cn(
                  "flex-[60%]",
                  feature.position === "left" ? "lg:col-start-2" : "",
                )}
              >
                <h3 className='mb-6 text-3xl font-bold leading-tight tracking-tight text-foreground md:text-4xl lg:text-[32px]'>
                  {feature.title}
                </h3>
                <p className='text-body mb-8 leading-relaxed text-muted-foreground'>
                  {feature.description}
                </p>
                <GradientBorderButton className='z-[1]'>
                  {feature.cta}
                </GradientBorderButton>
              </div>

              {/* Image */}
              <div
                className={`relative flex-[40%] ${feature.position === "left" ? "lg:col-start-1" : ""}`}
              >
                {/* Background gradients */}
                <div className='absolute left-[10%] top-0 block min-h-[200px] min-w-[80px] translate-y-1/2 bg-[#F16B6B] opacity-50 blur-[80px]' />
                <div className='absolute right-[10%] top-0 block min-h-[200px] min-w-[80px] translate-y-1/2 bg-[#EB20D1] opacity-50 blur-[80px]' />

                <div className='relative transform overflow-hidden rounded-2xl'>
                  <Image
                    src={feature.image}
                    alt={`${feature.title} mockup - Dashboard interface`}
                    width={600}
                    height={400}
                    className='h-auto w-full'
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
