"use client"

import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Image from "next/image"
import { useEffect, useRef } from "react"
import { features } from "../../../constants/features"
import GradientBorderButton from "../../custom/gradient-border-button"

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger)
}

export default function DesktopFeaturesSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const frameRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const contentRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    if (
      !sectionRef.current ||
      !containerRef.current ||
      !frameRef.current ||
      !imageRef.current
    )
      return

    const ctx = gsap.context(() => {
      // Animate section title
      gsap.fromTo(
        ".feature-title",
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
          },
        },
      )

      // Initial setup - hide all content and set frame to center
      gsap.set(contentRefs.current, { opacity: 0 })
      gsap.set(frameRef.current, { x: 0 }) // Start at center

      // Create smooth frame animation with GSAP timeline
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: containerRef.current,
          start: "top top",
          end: "+=400%", // Long scroll for smooth animation
          scrub: 1,
          pin: true,
          pinSpacing: true,
        },
      })

      // Define frame positions with both X and Y movement for dynamic effect
      const framePositions = [
        { x: 0, y: 0, rotation: 0 }, // Center start
        { x: "25vw", y: "10vh", rotation: 5 }, // Right + slightly up
        { x: "-25vw", y: "10vh", rotation: -5 }, // Left + slightly down
        { x: "30vw", y: "5vh", rotation: 3 }, // Right + slight up
        { x: "-20vw", y: "5vh", rotation: -3 }, // Left + slight down
      ]

      // Initial setup
      gsap.set(contentRefs.current, { opacity: 0 })
      gsap.set(frameRef.current, framePositions[0])

      // Animate through each position
      framePositions.forEach((position, index) => {
        if (index === 0) return // Skip first position (already set)

        tl.to(frameRef.current, {
          x: position.x,
          y: position.y,
          rotation: position.rotation,
          duration: 1,
          ease: "power3.inOut",
        }).call(
          () => {
            // Update image and content for each step
            if (imageRef.current) {
              const imageIndex = Math.min(index + 1, 4)
              imageRef.current.src = `/images/features/image${imageIndex}.webp`
            }

            // Show corresponding content
            gsap.set(contentRefs.current, { opacity: 0 })
            if (index <= features.length && contentRefs.current[index - 1]) {
              gsap.set(contentRefs.current[index - 1], { opacity: 1 })
            }
          },
          null,
          index * 1,
        ) // Offset calls
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section
      ref={sectionRef}
      className='px-2 py-24 md:py-[100px]'
      id='features'
    >
      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Section Header */}
        <div className='mb-16 space-y-6 text-center md:mb-24 md:space-y-8'>
          <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-2.5 text-muted-foreground'>
            Features
          </p>
          <h2 className='text-section-title feature-title'>
            The Smartest Way to Try
            <br />
            Before You Buy
          </h2>
          <p className='text-body feature-title mx-auto max-w-3xl text-primary'>
            From immersive product previews to seamless integration, Prism
            Try-On tool is built to boost engagement, sales, and customer
            satisfaction - all while fitting effortlessly into your store&apos;s
            workflow.
          </p>
        </div>

        {/* Full Screen Animation Container */}
        <div
          ref={containerRef}
          className='relative h-screen w-full overflow-hidden'
        >
          {/* Moving Frame - Always centered vertically, moves horizontally */}
          <div
            ref={frameRef}
            className='absolute left-1/2 top-1/2 z-20 -translate-x-1/2 -translate-y-1/2'
          >
            <div className='relative'>
              {/* Background gradients for frame */}
              <div className='absolute left-[10%] top-0 block min-h-[200px] min-w-[80px] translate-y-1/2 bg-[#F16B6B] opacity-50 blur-[80px]' />
              <div className='absolute right-[10%] top-0 block min-h-[200px] min-w-[80px] translate-y-1/2 bg-[#EB20D1] opacity-50 blur-[80px]' />

              {/* Frame with dynamic image */}
              <div className='relative transform overflow-hidden rounded-2xl'>
                <Image
                  src='/images/features/frame.webp'
                  alt='Frame'
                  width={600}
                  height={400}
                  className='h-auto w-full md:w-96'
                />
                <Image
                  ref={imageRef}
                  src='/images/features/image1.webp'
                  alt='Dynamic content'
                  width={500}
                  height={350}
                  className='absolute left-1/2 top-1/2 h-auto w-4/5 -translate-x-1/2 -translate-y-1/2 rounded-lg'
                />
              </div>
            </div>
          </div>

          {/* Feature Content Cards - Traditional 50/50 Layout */}
          {features.map((feature, index) => (
            <div
              key={index}
              ref={(el) => {
                contentRefs.current[index] = el
              }}
              className={`absolute inset-0 flex items-center gap-12 px-8 md:gap-20 ${
                feature.position === "left" ? "md:flex-row-reverse" : ""
              }`}
            >
              {/* Content Section - 50% */}
              <div className='flex-1 space-y-6'>
                <h3 className='text-3xl font-bold leading-tight tracking-tight text-foreground md:text-4xl lg:text-[32px]'>
                  {feature.title}
                </h3>
                <p className='text-body leading-relaxed text-muted-foreground'>
                  {feature.description}
                </p>
                <GradientBorderButton className='z-[1]'>
                  {feature.cta}
                </GradientBorderButton>
              </div>

              {/* Image Section - 50% (Placeholder for moving frame) */}
              <div className='flex flex-1 items-center justify-center'>
                {/* This space is where the moving frame will visually appear */}
                <div className='h-64 w-96 opacity-0'>
                  {/* Invisible placeholder to maintain layout */}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
