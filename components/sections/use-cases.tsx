"use client"

import CheckmarkIcon from "@/components/icons/check-mark-icon"
import { USE_CASES } from "@/constants/use-cases"
import { motion } from "framer-motion"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Image from "next/image"
import { useEffect, useRef, useState } from "react"

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger)
}

interface UseCase {
  id: string
  name: string
  title: string
  description: string
  features: string[]
  image: string
  icon: string
}

interface UseCaseCardProps {
  useCase: UseCase
  index: number
  totalCards: number
  isActive: boolean
  onClick: () => void
  activeIndex: number
}

function UseCaseCard({
  useCase,
  index,
  totalCards,
  isActive,
  onClick,
  activeIndex,
}: UseCaseCardProps) {
  const actualIndex = isActive ? 0 : index > activeIndex ? index : index + 1
  const translateY = actualIndex * -70
  const zIndex = totalCards - actualIndex
  // Scale: active card = 1.0, background cards scale down progressively
  const scale = isActive ? 1.0 : 1.0 - actualIndex * 0.05

  return (
    <motion.div
      className='absolute left-0 top-0 w-full'
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      animate={{
        opacity: 1,
        y: translateY,
        scale: scale,
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 25,
      }}
      style={{
        zIndex,
      }}
      whileHover={{
        y: translateY - 5,
        scale: isActive ? 1.02 : scale + 0.02,
        transition: { duration: 0.2 },
      }}
      onClick={onClick}
    >
      {/* Card */}
      <div className='cursor-pointer overflow-hidden rounded-2xl bg-white shadow-xl transition-all duration-300'>
        {/* Card Header */}
        <div className='flex items-center gap-3 rounded-2xl bg-[#F6F5F4] p-4 md:gap-4 md:p-6'>
          {/* <div className='text-2xl md:text-3xl'>{useCase.icon}</div> */}
          <Image
            src={useCase.icon}
            alt={useCase.title + "icon"}
            width={50}
            height={50}
            className='h-auto w-[20px]'
          />
          <div className='flex-1'>
            <h3 className='text-base font-semibold text-muted-foreground md:text-[16.5px]'>
              {useCase.name}
            </h3>
          </div>
        </div>

        {/* Card Content */}
        <div className='p-4 md:p-8'>
          <div className='grid items-center gap-6 md:gap-12 lg:grid-cols-2'>
            {/* Image - Shows first on mobile, second on desktop */}
            <div className='relative order-1 lg:order-2'>
              <div className='relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-purple-50'>
                <Image
                  src={useCase.image}
                  alt={useCase.title}
                  width={600}
                  height={400}
                  className='h-auto w-full'
                  sizes='(max-width: 1024px) 100vw, 50vw'
                />
              </div>
            </div>

            {/* Content - Shows second on mobile, first on desktop */}
            <div className='order-2 lg:order-1'>
              <h4 className='mb-3 text-xl font-bold text-foreground md:mb-4 md:text-2xl lg:text-3xl'>
                {useCase.title}
              </h4>

              <p className='mb-6 text-base text-muted-foreground md:mb-8 md:text-lg'>
                {useCase.description}
              </p>

              <div className='space-y-3 md:space-y-4'>
                {useCase.features.map((feature, featureIndex) => (
                  <div
                    key={featureIndex}
                    className='flex items-start gap-3 md:gap-4'
                  >
                    <div className='mt-0.5 flex-shrink-0 md:mt-1'>
                      <CheckmarkIcon
                        width={16}
                        height={16}
                        // color='#F16B6B'
                        className='fill-[#F16B6B] md:h-4 md:w-4'
                      />
                    </div>
                    <p className='md:text-body-small text-sm text-muted-foreground'>
                      {feature}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default function UseCasesSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const [activeCardIndex, setActiveCardIndex] = useState(0)

  useEffect(() => {
    if (!sectionRef.current) return

    const ctx = gsap.context(() => {
      gsap.fromTo(
        ".usecase-title",
        { opacity: 0, y: 10 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
          },
        },
      )

      gsap.fromTo(
        ".tab-header",
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.1,
          scrollTrigger: {
            trigger: ".tabs-container",
            start: "top 80%",
          },
        },
      )

      gsap.fromTo(
        ".tab-content",
        { opacity: 0, scale: 0.95 },
        {
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: ".tab-content",
            start: "top 80%",
          },
        },
      )
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  const handleCardClick = (clickedIndex: number) => {
    if (clickedIndex !== activeCardIndex) {
      setActiveCardIndex(clickedIndex)
    }
  }

  return (
    <section
      ref={sectionRef}
      className='relative overflow-hidden bg-white py-20 md:py-32'
      id='use-cases'
    >
      {/* Gradient Background Effects */}
      {/* Bottom Left Gradient - Red/Pink */}
      <div className='absolute bottom-0 left-[10%] block min-h-[200px] min-w-[100px] -translate-y-1/2 bg-[#F16B6B] opacity-25 blur-[120px] md:min-h-[400px] md:min-w-[200px]' />

      {/* Top Right Gradient - Purple/Pink */}
      <div className='absolute right-[10%] top-0 block min-h-[200px] min-w-[100px] translate-y-1/2 bg-[#EB20D1] opacity-25 blur-[120px] md:min-h-[400px] md:min-w-[200px]' />

      {/* Gradient overlay for fading effect */}
      <div className='pointer-events-none absolute inset-0 bg-gradient-to-b from-white via-transparent to-white opacity-60' />

      <div className='container relative mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Section Header */}
        <div className='mb-16 space-y-6 text-center md:mb-24 md:space-y-8'>
          <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-2.5 text-muted-foreground'>
            Use Cases
          </p>
          <h2 className='text-section-title usecase-title'>
            Powerful Virtual
            <br />
            Try-On Use Cases
          </h2>
          <p className='text-body feature-title mx-auto max-w-xl text-muted-foreground'>
            See how our AI virtual try-on boosts sales, reduces returns, and
            enhances shopping experiences across retail, marketing, and digital
            platforms.
          </p>
        </div>

        {/* Stacked Cards Container */}
        <div className='tabs-container relative z-10 mx-auto mt-16 max-w-6xl md:mt-56'>
          {/* Stacked Cards */}
          <div
            className='relative'
            style={{ height: `${700 + (USE_CASES.length - 1) * 30}px` }}
          >
            {USE_CASES.map((useCase, index) => (
              <UseCaseCard
                key={useCase.id}
                useCase={useCase}
                index={index}
                totalCards={USE_CASES.length}
                isActive={index === activeCardIndex}
                onClick={() => handleCardClick(index)}
                activeIndex={activeCardIndex}
              />
            ))}

            {/* Fade gradient overlay at the top to make background cards appear faded */}
            <div className='pointer-events-none absolute left-0 right-0 top-[-150px] z-20 h-40 bg-gradient-to-b from-white via-white/80 to-transparent blur-2xl' />
          </div>
        </div>
      </div>
    </section>
  )
}
