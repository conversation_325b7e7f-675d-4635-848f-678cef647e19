"use client"

import { TESTIMONIALS } from "@/constants/testimonials"
import TestimonialCard from "../cards/testimonial-card"
import { Marquee } from "../ui/marquee"

export default function TestimonialsSection() {
  return (
    <section
      className='relative overflow-hidden py-16 md:py-20'
      id='testimonials'
    >
      {/* Gradient Background Effects */}
      {/* Bottom Left Gradient - Red/Pink */}
      <div className='absolute bottom-0 right-[10%] block min-h-[200px] min-w-[100px] -translate-y-1/2 bg-[#F16B6B] opacity-10 blur-[120px] md:min-h-[400px] md:min-w-[200px]' />

      {/* Top Right Gradient - Purple/Pink */}
      {/* <div className='absolute right-[10%] top-0 block min-h-[200px] min-w-[100px] translate-y-1/2 bg-[#EB20D1] opacity-20 blur-[120px] md:min-h-[400px] md:min-w-[200px]' /> */}

      <div className='relative'>
        {/* Section Header */}
        <div className='container mb-16 space-y-6 text-center md:mb-16 md:space-y-8'>
          <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-2.5 text-muted-foreground'>
            Testimonial
          </p>
          <h2 className='mb-6 font-afacad text-2xl font-bold md:text-3xl lg:text-4xl'>
            Trusted by leading brands to power virtual
            <br />
            try-on experiences worldwide.
          </h2>
        </div>
        {/* Testimonials Marquee */}
        <div className='relative'>
          <Marquee pauseOnHover className='[--duration:30s]'>
            {TESTIMONIALS.map((testimonial, index) => (
              <TestimonialCard key={index} testimonial={testimonial} />
            ))}
          </Marquee>
        </div>
      </div>

      {/* Bottom Quote */}
      {/* <div className='mt-16 text-center'>
        <blockquote className='mx-auto max-w-4xl font-afacad text-lg italic text-muted-foreground md:text-xl'>
          &apos;We added the AI virtual try-on button to our fashion product
          pages in under 10 min. Our online clothing conversion rate increased
          by 45% in the first month! &apos;
        </blockquote>
      </div> */}
    </section>
  )
}
