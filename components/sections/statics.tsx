import { timelineData } from "@/constants/impact-stats"
import Image from "next/image"

export default function Statics() {
  return (
    <>
      {/* Timeline Layout for Desktop */}

      <div className='stats-container hidden py-16 lg:block'>
        <div className='relative mx-auto max-w-6xl px-4'>
          {/* Horizontal line */}
          <div className='absolute left-0 right-0 top-16 h-px bg-white/20'></div>

          {/* Timeline items */}
          <div className='relative flex justify-between'>
            {timelineData.map((item, index) => (
              <div
                key={index}
                className='stat-card relative flex flex-col items-center'
                style={{ width: `${100 / timelineData.length}%` }}
              >
                {/* Image ON the horizontal line */}
                <div className='absolute left-1/2 top-16 z-20 -translate-x-1/2 -translate-y-1/2 transform'>
                  <Image
                    src={item.image}
                    alt={item.label}
                    width={200}
                    height={200}
                    className={
                      index === 0
                        ? "h-[80px] w-[120px] object-contain"
                        : "h-[50px] w-[50px]"
                    }
                  />
                </div>

                {/* Content below the line and image */}
                <div className='mt-24 px-2 text-center'>
                  {/* Label */}
                  <h4 className='mb-2 text-2xl font-bold text-white'>
                    {item.label}
                  </h4>

                  {/* Description */}
                  <p className='max-w-[150px] text-sm leading-relaxed text-gray-400'>
                    {item.description}
                  </p>
                </div>
              </div>
            ))}

            {/* Dots BETWEEN timeline items - only 3 dots for 4 items */}
            {Array.from({ length: timelineData.length - 1 }, (_, index) => (
              <div
                key={`dot-${index}`}
                className='absolute top-16 z-10 h-4 w-4 -translate-y-1/2 transform rounded-full border-2 border-white/20 bg-black'
                style={{
                  left: `${((index + 1) * 100) / timelineData.length}%`,
                  transform: "translateX(-50%) translateY(-50%)",
                }}
              ></div>
            ))}
          </div>
        </div>
      </div>

      <div className='stats-container mx-auto block max-w-md space-y-8 lg:hidden'>
        {timelineData.map((item, index) => (
          <div key={index} className='stat-card text-center'>
            {/* Image */}
            <div className='mb-4'>
              <Image
                src={item.image}
                alt={item.label}
                width={100}
                height={60}
                className='mx-auto object-cover'
              />
            </div>

            {/* Label */}
            <h4 className='mb-2 text-lg font-semibold text-white'>
              {item.label}
            </h4>

            {/* Description */}
            <p className='text-sm text-gray-400'>{item.description}</p>
          </div>
        ))}
      </div>
    </>
  )
}
