"use client"

import CheckmarkIcon from "@/components/icons/check-mark-icon"
import { USE_CASES } from "@/constants/use-cases"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Image from "next/image"
import { useEffect, useRef, useState } from "react"

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger)
}

export default function UseCasesSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const [activeTab, setActiveTab] = useState(2) // Default to Consumer Software Integration (index 2)

  useEffect(() => {
    if (!sectionRef.current) return

    const ctx = gsap.context(() => {
      gsap.fromTo(
        ".usecase-title",
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
          },
        },
      )

      gsap.fromTo(
        ".tab-header",
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.1,
          scrollTrigger: {
            trigger: ".tabs-container",
            start: "top 80%",
          },
        },
      )

      gsap.fromTo(
        ".tab-content",
        { opacity: 0, scale: 0.95 },
        {
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: ".tab-content",
            start: "top 80%",
          },
        },
      )
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  const handleTabClick = (tabIndex: number) => {
    setActiveTab(tabIndex)

    // Animate tab content change
    gsap.fromTo(
      ".tab-content-inner",
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.5, ease: "power3.out" },
    )
  }

  return (
    <section
      ref={sectionRef}
      className='bg-white py-20 md:py-32'
      id='use-cases'
    >
      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Section Header */}
        <div className='mb-16 space-y-6 text-center md:mb-24 md:space-y-8'>
          <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-2.5 text-muted-foreground'>
            Use Cases
          </p>
          <h2 className='text-section-title usecase-title'>
            Powerful Virtual
            <br />
            Try-On Use Cases
          </h2>
          <p className='text-body feature-title mx-auto max-w-xl text-muted-foreground'>
            See how our AI virtual try-on boosts sales, reduces returns, and
            enhances shopping experiences across retail, marketing, and digital
            platforms.
          </p>
        </div>

        {/* Desktop Tab Container */}
        <div className='tabs-container mx-auto max-w-6xl'>
          {/* Tab Headers */}
          <div className='mb-8 flex justify-center gap-4'>
            {USE_CASES.map((useCase, index) => (
              <button
                key={useCase.id}
                onClick={() => handleTabClick(index)}
                className={`tab-header flex min-w-[200px] flex-col items-center rounded-t-xl border-b-4 p-6 transition-all duration-300 ${
                  activeTab === index
                    ? "border-blue-600 bg-white shadow-lg"
                    : "border-transparent bg-gray-50 hover:bg-gray-100"
                }`}
              >
                <div
                  className={`mb-2 text-3xl ${activeTab === index ? "scale-110" : ""} transition-transform duration-300`}
                >
                  {useCase.icon}
                </div>
                <span
                  className={`text-sm font-medium ${
                    activeTab === index ? "text-blue-600" : "text-gray-600"
                  }`}
                >
                  {useCase.name}
                </span>
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className='tab-content overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-xl'>
            <div className='tab-content-inner p-8'>
              <div className='grid items-center gap-12 lg:grid-cols-2'>
                {/* Left Side - Content */}
                <div>
                  <h3 className='mb-4 text-3xl font-bold text-foreground md:text-4xl'>
                    {USE_CASES[activeTab].title}
                  </h3>

                  <p className='mb-8 text-lg text-muted-foreground'>
                    {USE_CASES[activeTab].description}
                  </p>

                  <div className='space-y-4'>
                    {USE_CASES[activeTab].features.map((feature, index) => (
                      <div key={index} className='flex items-start gap-4'>
                        <div className='mt-1 flex-shrink-0'>
                          <CheckmarkIcon
                            width={20}
                            height={20}
                            color='#10b981'
                          />
                        </div>
                        <p className='text-body-small text-muted-foreground'>
                          {feature}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Right Side - Image */}
                <div className='relative'>
                  <div className='relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-purple-50'>
                    <Image
                      src={USE_CASES[activeTab].image}
                      alt={USE_CASES[activeTab].title}
                      width={600}
                      height={400}
                      className='h-auto w-full'
                      sizes='(max-width: 1024px) 100vw, 50vw'
                    />

                    {/* Overlay Elements */}
                    <div className='absolute right-4 top-4 rounded-lg bg-white px-3 py-2 shadow-lg'>
                      <div className='flex items-center gap-2'>
                        <div className='h-2 w-2 animate-pulse rounded-full bg-green-500'></div>
                        <span className='text-xs font-medium text-gray-700'>
                          Live Try-On
                        </span>
                      </div>
                    </div>

                    <div className='absolute bottom-4 left-4 rounded-lg bg-white px-3 py-2 shadow-lg'>
                      <div className='flex items-center gap-2'>
                        <div className='flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-purple-500 to-pink-500'>
                          <span className='text-xs font-bold text-white'>
                            AI
                          </span>
                        </div>
                        <span className='text-xs font-medium text-gray-700'>
                          Processing...
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
