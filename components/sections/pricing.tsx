"use client"

import PlanCard from "../cards/plan-card"

export default function PricingSection() {
  const plans = [
    {
      name: "Basic",
      price: 24,
      period: "One-time payment",
      features: ["No Setup Required", "One-Line Integration", "Cancel Anytime"],
      popular: false,
      variant: "outline" as const,
    },
    {
      name: "Basic",
      price: 24,
      period: "One-time payment",
      features: ["Lorem Ipsum", "Lorem Ipsum", "Lorem Ipsum"],
      popular: true,
      variant: "gradient" as const,
    },
  ]

  return (
    <section className='bg-white py-20 md:py-32' id='pricing'>
      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Section Header */}
        <div className='mb-16 space-y-6 text-center md:mb-24 md:space-y-8'>
          <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-2.5 text-muted-foreground'>
            Pricing
          </p>
          <h2 className='text-section-title'>Choose a Plan</h2>
          <p className='text-body mx-auto max-w-2xl text-muted-foreground'>
            Whether you&apos;re starting small or scaling fast, our virtual
            try-on tool is priced to deliver maximum value and ROI.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className='mx-auto flex max-w-2xl flex-col justify-center gap-6 md:flex-row'>
          {plans.map((plan, index) => (
            <PlanCard key={index} plan={plan} />
          ))}
        </div>

        {/* Bottom Text */}
        {/* <div className='mt-16 text-center'>
          <p className='text-body-small text-muted-foreground'>
            All plans include 24/7 support and 30-day money-back guarantee
          </p>
        </div> */}
      </div>
    </section>
  )
}
