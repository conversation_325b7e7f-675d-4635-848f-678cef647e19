"use client"

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion"

export default function FAQSection() {
  const faqs = [
    {
      question: "What's Virtual Try On?",
      answer:
        "The term Virtual Try On mentioned is an advanced AI image manipulation technique. With it and the work of SilverAI' engineers, FitRoom hopes to make changing outfits and fashion styles less burdensome for users.",
    },
    {
      question: "Can I use Prism Try-on on iPhone/Android?",
      answer:
        "Yes, Prism Try-On works seamlessly on both iOS and Android devices through your web browser. No app download required - simply access it through your mobile browser for the full experience.",
    },
    {
      question: "Can I customize the fit of the AI-generated try-on?",
      answer:
        "Absolutely! Our AI technology allows for customization of fit preferences, body type adjustments, and styling options to ensure the most accurate virtual try-on experience for each user.",
    },
    {
      question: "Can I change clothes in group photos?",
      answer:
        "Currently, our AI is optimized for individual try-on experiences. Group photo functionality is in development and will be available in future updates.",
    },
    {
      question: "How many outfits can it generate?",
      answer:
        "There's no limit to the number of outfits you can try on. Our AI can process unlimited clothing items and generate as many virtual try-on combinations as you need.",
    },
  ]

  return (
    <section className='bg-white py-20 md:py-32' id='faq'>
      <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='grid items-start gap-12 lg:grid-cols-2 lg:gap-20'>
          {/* Left Side - Title */}
          <div>
            <h2 className='text-faq-title mb-6 text-foreground'>FAQ</h2>
            <p className='text-body leading-relaxed text-muted-foreground'>
              Find quick answers about our AI Virtual Try-On tool, from setup to
              compatibility, so you can start transforming your store or website
              with ease.
            </p>
          </div>

          {/* Right Side - FAQ Accordion */}
          <div>
            <Accordion type='single' collapsible className='space-y-4'>
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className='rounded-none border-b border-gray-200 px-0 pb-3 transition-colors duration-200 hover:border-gray-300'
                >
                  <AccordionTrigger className='py-6 text-left hover:no-underline'>
                    <div className='flex w-full items-center justify-between'>
                      <span className='pr-4 font-afacad text-base font-medium text-foreground md:text-lg'>
                        {faq.question}
                      </span>
                      <span className='font-mono text-sm text-muted-foreground'>
                        0{index + 1}
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className='pb-6'>
                    <div className='border-t border-gray-100 pt-6'>
                      <p className='text-body-small leading-relaxed text-muted-foreground'>
                        {faq.answer}
                      </p>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </div>
    </section>
  )
}
