"use client"
import { AnimatePresence, motion } from "framer-motion"

interface MobileStepContentProps {
  step: {
    number: number
    title: string
    description: string
    duration: number
    videoSrc: string
  }
}

export default function MobileStepContent({ step }: MobileStepContentProps) {
  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key={step.number}
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -30 }}
        transition={{ duration: 0.4, ease: "easeInOut" }}
        className='text-center'
      >
        {/* Step Title with Gradient Text */}
        <h3 className='mb-6 px-3 text-3xl font-bold leading-tight text-black'>
          {step.title}
        </h3>

        {/* Step Description */}
        <p className='mx-auto min-h-[70px] max-w-md px-2 text-[16px] leading-relaxed text-muted-foreground'>
          {step.description}
        </p>
      </motion.div>
    </AnimatePresence>
  )
}
