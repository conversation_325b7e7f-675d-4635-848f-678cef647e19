"use client"

interface VideoPlayerProps {
  activeStep: number
  steps: Array<{
    number: number
    title: string
    description: string
    duration: number
    videoSrc: string
  }>
}

export default function VideoPlayer({ activeStep, steps }: VideoPlayerProps) {
  const currentStep = steps[activeStep - 1]

  return (
    <div
      className='relative aspect-[4/3] rounded-lg p-[2px] shadow-sm transition-all duration-300'
      style={{
        background: `linear-gradient(110.43deg, rgba(255, 255, 255, 0.3) 5.85%, rgba(255, 255, 255, 0.1) 94.01%), linear-gradient(89.7deg, #62CDFF -43.29%, #7D42A5 17.93%, #FF988A 116.46%, #E4398A 155.68%)`,
      }}
    >
      <div className='relative h-full w-full overflow-hidden rounded-lg bg-white'>
        <div className='relative h-full w-full'>
          <video
            key={activeStep}
            className='h-full w-full rounded-lg object-cover'
            autoPlay
            muted
            loop
            playsInline
          >
            <source src={currentStep?.videoSrc} type='video/mp4' />
            {/* Fallback content */}
            <div
              className='flex h-full w-full items-center justify-center rounded-lg'
              style={{
                background: `linear-gradient(135deg, rgba(98, 205, 255, 0.1) 0%, rgba(125, 66, 165, 0.1) 50%, rgba(228, 57, 138, 0.1) 100%)`,
              }}
            >
              <div className='text-center'>
                <div className='mb-4 text-6xl'>
                  {activeStep === 1 ? "👕" : activeStep === 2 ? "📷" : "✨"}
                </div>
                <h3 className='text-xl font-semibold text-gray-800'>
                  {currentStep?.title}
                </h3>
                <p className='mt-2 text-gray-600'>Video demonstration</p>
              </div>
            </div>
          </video>
        </div>
      </div>
    </div>
  )
}
