"use client"
import { motion } from "framer-motion"

interface MobileStepNavigationProps {
  steps: Array<{
    number: number
    title: string
    description: string
    duration: number
    videoSrc: string
  }>
  activeStep: number
  // eslint-disable-next-line no-unused-vars
  onStepClick: (stepNumber: number) => void
  progress: number
}

export default function MobileStepNavigation({
  steps,
  activeStep,
  onStepClick,
  progress,
}: MobileStepNavigationProps) {
  return (
    <div className='flex justify-center gap-6 px-6'>
      {steps.map((step) => {
        const isActive = activeStep === step.number
        const isCompleted = activeStep > step.number

        return (
          <div key={step.number} className='flex flex-col items-center'>
            {/* Step Number with Gradient Border as Progress Indicator */}
            <div
              onClick={() => onStepClick(step.number)}
              className='relative cursor-pointer transition-transform duration-200 hover:scale-105'
            >
              {/* Circular Progress Background */}
              <svg
                className='absolute inset-0 h-8 w-8 -rotate-90 transform'
                viewBox='0 0 36 36'
              >
                {/* Background circle */}
                <circle
                  cx='18'
                  cy='18'
                  r='16'
                  fill='none'
                  stroke='rgba(229, 231, 235, 0.3)'
                  strokeWidth='2'
                />
                {/* Progress circle - only show for active step */}
                {isActive && (
                  <motion.circle
                    cx='18'
                    cy='18'
                    r='16'
                    fill='none'
                    stroke='url(#gradient)'
                    strokeWidth='1'
                    strokeLinecap='round'
                    strokeDasharray='100.48'
                    initial={{ strokeDashoffset: 100.48 }}
                    animate={{
                      strokeDashoffset: 100.48 - (progress / 100) * 100.48,
                    }}
                    transition={{ duration: 0.1, ease: "linear" }}
                  />
                )}
                {/* Gradient definition */}
                <defs>
                  <linearGradient
                    id='gradient'
                    x1='0%'
                    y1='0%'
                    x2='100%'
                    y2='0%'
                  >
                    <stop offset='0%' stopColor='#62CDFF' />
                    <stop offset='30%' stopColor='#7D42A5' />
                    <stop offset='70%' stopColor='#FF988A' />
                    <stop offset='100%' stopColor='#E4398A' />
                  </linearGradient>
                </defs>
              </svg>

              {/* Step Number Circle */}
              <div
                className='h-8 w-8 rounded-full p-[1px] shadow-lg transition-all duration-300'
                style={{
                  background:
                    isActive || isCompleted
                      ? `linear-gradient(110.43deg, rgba(255, 255, 255, 0.8) 5.85%, rgba(255, 255, 255, 0.2) 94.01%), linear-gradient(89.7deg, #62CDFF -43.29%, #7D42A5 17.93%, #FF988A 116.46%, #E4398A 155.68%)`
                      : "rgba(229, 231, 235, 0.6)",
                  filter: isActive
                    ? "drop-shadow(0 4px 12px rgba(125, 66, 165, 0.3))"
                    : "none",
                }}
              >
                <div className='flex h-full w-full items-center justify-center rounded-full bg-white'>
                  <span
                    className={`text-sm font-bold transition-colors duration-300 ${
                      isActive || isCompleted
                        ? "bg-gradient-to-r from-[#62CDFF] via-[#7D42A5] to-[#E4398A] bg-clip-text text-transparent"
                        : "text-gray-400"
                    }`}
                  >
                    {step.number}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
