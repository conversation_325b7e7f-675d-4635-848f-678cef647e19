"use client"
import { AnimatePresence, motion } from "framer-motion"
import { ArrowRightIcon } from "lucide-react"
import { cn } from "../../../lib/utils"

interface StepItemProps {
  step: {
    number: number
    title: string
    description: string
    duration: number
    videoSrc: string
  }
  isActive: boolean
  isCompleted: boolean
  progress: number
  // eslint-disable-next-line no-unused-vars
  onStepClick: (stepNumber: number) => void
}

export default function StepItem({
  step,
  isActive,
  isCompleted,
  progress,
  onStepClick,
}: StepItemProps) {
  return (
    <div className='relative'>
      {/* Progress Bar - Top Border */}
      <div className='relative mb-6 h-[2.5px] w-full overflow-hidden rounded-full bg-gray-200'>
        <motion.div
          className={cn("h-full", isActive ? "opacity-100" : "opacity-20")}
          style={{
            background: `linear-gradient(89.7deg, #62CDFF -43.29%, #7D42A5 17.93%, #FF988A 116.46%, #E4398A 155.68%)`,
          }}
          initial={{ width: "0%" }}
          animate={{
            width: isActive ? `${progress}%` : isCompleted ? "100%" : "0%",
          }}
          transition={{ duration: 0.1, ease: "linear" }}
        />
      </div>

      {/* Step Content - No Background */}
      <div
        onClick={() => onStepClick(step.number)}
        className={cn(
          "cursor-pointer py-4 transition-all duration-300",
          isActive ? "opacity-100" : "opacity-40",
        )}
        aria-current={isActive ? "step" : undefined}
      >
        <div className='flex items-center justify-between gap-6'>
          <div className='flex flex-1 items-center justify-start gap-4'>
            {/* Step Number with Custom Gradient Border */}
            <div className='relative flex-shrink-0'>
              <div
                className='h-[42px] w-[42px] rounded-full p-[0.88px] transition-all duration-300'
                style={{
                  background:
                    isActive || isCompleted
                      ? `linear-gradient(110.43deg, rgba(255, 255, 255, 0.6) 5.85%, rgba(255, 255, 255, 0.1) 94.01%), linear-gradient(89.7deg, #62CDFF -43.29%, #7D42A5 17.93%, #FF988A 116.46%, #E4398A 155.68%)`
                      : "rgba(229, 231, 235, 0.5)",
                }}
              >
                <div className='flex h-full w-full items-center justify-center rounded-full bg-white'>
                  <span
                    className={`text-[23.4px] transition-colors duration-300 ${
                      isActive
                        ? "bg-gradient-to-r from-[#62CDFF] via-[#7D42A5] to-[#E4398A] bg-clip-text text-transparent"
                        : isCompleted
                          ? "bg-gradient-to-r from-[#62CDFF] via-[#7D42A5] to-[#E4398A] bg-clip-text text-transparent"
                          : "text-gray-400"
                    }`}
                  >
                    {step.number}
                  </span>
                </div>
              </div>
            </div>

            <h3
              className={`text-xl font-bold transition-colors duration-300 md:text-2xl ${
                isActive ? "text-foreground" : "text-gray-500"
              }`}
            >
              {step.title}
            </h3>
          </div>

          {/* Arrow Icon */}
          <ArrowRightIcon
            className={`h-6 w-6 transition-all duration-300 ${
              isActive ? "-rotate-45 text-[#7D42A5]" : "rotate-45 text-gray-400"
            }`}
          />
        </div>

        {/* Step Description with Animation */}
        <AnimatePresence>
          {isActive && (
            <motion.div
              key={step.number}
              initial={{ opacity: 0, height: 0, marginTop: 0 }}
              animate={{ opacity: 1, height: "auto", marginTop: 24 }}
              exit={{ opacity: 0, height: 0, marginTop: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className='overflow-hidden'
            >
              <div className='ml-16 pr-12'>
                <p className='text-lg leading-relaxed text-muted-foreground'>
                  {step.description}
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
