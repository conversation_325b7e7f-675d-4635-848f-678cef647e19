"use client"

import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import Image from "next/image"
import { useEffect, useRef } from "react"
import GradientBorderButton from "../custom/gradient-border-button"
import Statics from "./statics"

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger)
}

export default function ImpactBusinessSection() {
  const sectionRef = useRef<HTMLDivElement>(null)
  const leftFloatingRef = useRef<HTMLDivElement>(null)
  const rightFloatingRef = useRef<HTMLDivElement>(null)
  // const dashboardRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (
      !sectionRef.current ||
      !leftFloatingRef.current ||
      !rightFloatingRef.current
    )
      return

    const ctx = gsap.context(() => {
      // Left floating element animation
      gsap.to(leftFloatingRef.current, {
        y: -30,
        duration: 2,
        ease: "power2.inOut",
        repeat: -1,
        yoyo: true,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          scrub: 1,
        },
      })

      // Right floating element animation (opposite direction)
      gsap.to(rightFloatingRef.current, {
        y: 30,
        duration: 2.5,
        ease: "power2.inOut",
        repeat: -1,
        yoyo: true,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          scrub: 1,
        },
      })

      // Stats cards stagger animation
      gsap.fromTo(
        ".stat-card",
        {
          opacity: 0,
          y: 50,
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          stagger: 0.2,
          scrollTrigger: {
            trigger: ".stats-container",
            start: "top 80%",
            end: "bottom 60%",
          },
        },
      )
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section
      ref={sectionRef}
      className='relative overflow-hidden bg-primary py-20 text-white md:py-28'
    >
      {/* Background Pattern */}
      <div className='absolute inset-0 opacity-10'>
        <div className='absolute left-10 top-20 h-64 w-64 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 blur-3xl'></div>
        <div className='absolute bottom-20 right-10 h-96 w-96 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 blur-3xl'></div>
      </div>

      <div className='container relative z-10 mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Section Header */}
        <div className='mb-16 space-y-6 text-center md:mb-24 md:space-y-8'>
          <p className='text-caption mx-auto w-max rounded-full border border-[#3D3D3D] px-6 py-2.5 text-white'>
            Impact
          </p>
          <h2 className='text-section-title mb-6'>
            What <span className='gradient-text'>Prism Try-On</span> can do
            <br />
            to your Business
          </h2>
          <p className='text-body mx-auto mb-8 max-w-3xl text-gray-300'>
            Deliver an engaging, confidence-boosting shopping experience that
            increases cart value, keeps customers browsing longer, and reduces
            returns.
          </p>
          <div className='flex items-center justify-center'>
            <GradientBorderButton className='z-[1]'>
              BUY NOW
            </GradientBorderButton>
          </div>
        </div>

        {/* Main Content */}
        <div className='mb-20'>
          {/* Dashboard Mockup */}
          <div className='relative mx-auto mb-16 max-w-5xl'>
            {/* Glow Effects */}
            <div className='absolute -inset-8 overflow-hidden rounded-2xl'>
              {/* Red glow - top left */}
              <div
                className='absolute -left-12 -top-12 h-32 w-32 rounded-full blur-3xl'
                style={{
                  background:
                    "radial-gradient(circle, #FF2F2F80 0%, #FF2F2F30 50%, transparent 100%)",
                }}
              ></div>
              {/* Purple glow - bottom right */}
              <div
                className='absolute -bottom-12 -right-12 h-32 w-32 rounded-full blur-3xl'
                style={{
                  background:
                    "radial-gradient(circle, #8A43E180 0%, #8A43E130 50%, transparent 100%)",
                }}
              ></div>
            </div>

            {/* Outer border with 50% opacity */}
            <div
              className='relative overflow-hidden rounded-2xl shadow-2xl'
              style={{
                background:
                  "linear-gradient(179deg, #FF2F2F40 0%, #EF7B1640 35.88%, #8A43E140 69.92%, #D511FD40 100%)",
                padding: "12px",
              }}
            >
              {/* Inner border with full colors */}
              <div
                className='relative overflow-hidden rounded-xl'
                style={{
                  background:
                    "linear-gradient(179deg, #FF2F2F 0%, #EF7B16 35.88%, #8A43E1 69.92%, #D511FD 100%)",
                  padding: "4px",
                }}
              >
                <div className='relative overflow-hidden rounded-lg bg-black'>
                  <Image
                    src='/images/impact-main.webp'
                    alt='Business Impact Dashboard'
                    width={800}
                    height={500}
                    className='h-auto w-full opacity-90'
                    sizes='100vw'
                  />

                  {/* Overlay gradient */}
                </div>
              </div>
            </div>

            {/* Floating Elements with Scroll Trigger */}
            <div
              ref={leftFloatingRef}
              className='absolute -left-16 bottom-[25%] h-auto rounded-full md:w-[250px]'
            >
              <Image
                src='/images/impact-left.png'
                alt='Business Impact Dashboard'
                width={400}
                height={100}
                className='h-auto w-full'
                sizes='100vw'
              />
            </div>

            <div
              ref={rightFloatingRef}
              className='absolute -right-16 bottom-[25%] h-auto rounded-full md:w-[296px]'
            >
              <Image
                src='/images/impact-right.png'
                alt='Business Impact Dashboard'
                width={500}
                height={200}
                className='h-auto w-full'
                sizes='100vw'
              />
            </div>
          </div>

          {/* statics */}
          <Statics />
        </div>
      </div>
    </section>
  )
}
