"use client"
import { useEffect, useMemo, useRef, useState } from "react"
import SectionWrapper from "../wrappers/section-wrapper"
import MobileStepContent from "./how-it-works/mobile-step-content"
import MobileStepNavigation from "./how-it-works/mobile-step-navigation"
import StepItem from "./how-it-works/step-item"
import VideoPlayer from "./how-it-works/video-player"

export default function HowItWorksSection() {
  const [activeStep, setActiveStep] = useState(1)
  // const [isPlaying, setIsPlaying] = useState(true)
  const [progress, setProgress] = useState(0)
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null)
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null)

  const steps = useMemo(
    () => [
      {
        number: 1,
        title: "Select your Outfit",
        description:
          "Browse and choose from our curated collection or upload your own clothing item. The AI will recognize it for the most accurate try-on experience.",
        duration: 4000,
        videoSrc: "/videos/step1-select-outfit.mp4",
      },
      {
        number: 2,
        title: "Upload your photo",
        description:
          "Take or upload a photo of yourself to see how the outfit looks on you.",
        duration: 3500,
        videoSrc: "/videos/step2-upload-photo.mp4",
      },
      {
        number: 3,
        title: "View your look",
        description:
          "See your personalized virtual try-on result in seconds with realistic fitting.",
        duration: 4500,
        videoSrc: "/videos/step3-view-look.mp4",
      },
    ],
    [],
  )

  // Auto-play functionality
  useEffect(() => {
    // if (!isPlaying) return

    const currentStep = steps.find((step) => step.number === activeStep)
    if (!currentStep) return

    const startProgress = () => {
      setProgress(0)

      const stepDuration = currentStep.duration
      const updateInterval = 50 // Update every 50ms for smooth animation
      const progressStep = (updateInterval / stepDuration) * 100

      intervalRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + progressStep
          if (newProgress >= 100) {
            // Move to next step
            const nextStep = activeStep < steps.length ? activeStep + 1 : 1
            setActiveStep(nextStep)
            return 0
          }
          return newProgress
        })
      }, updateInterval)
    }

    // Clear existing intervals
    if (intervalRef.current) clearInterval(intervalRef.current)
    if (timeoutRef.current) clearTimeout(timeoutRef.current)

    // Start progress after a small delay
    timeoutRef.current = setTimeout(startProgress, 100)

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
    }
  }, [activeStep, steps])

  const handleStepClick = (stepNumber: number) => {
    setActiveStep(stepNumber)
    setProgress(0)
  }

  // const togglePlayPause = () => {
  //   setIsPlaying(!isPlaying)
  // }

  return (
    <SectionWrapper
      id='how-it-works'
      className='container py-[50px] md:py-[100px]'
    >
      {/* Section Header */}
      <div className='mb-8 space-y-4 text-center md:mb-20 md:space-y-8'>
        <p className='text-caption mx-auto w-max rounded-full border border-gray-100 px-6 py-1.5 text-muted-foreground md:py-2.5'>
          Quick steps
        </p>
        <h2 className='text-section-title'>How it works</h2>
        <p className='mx-auto w-[90%] max-w-2xl px-4 text-[16px] text-muted-foreground md:text-lg'>
          From selection to virtual fit — see your look in seconds.
        </p>
      </div>

      {/* Content - Different layouts for mobile and desktop */}
      <div className='block md:hidden'>
        {/* Mobile Layout */}
        <div className='space-y-6 px-3'>
          {/* Video at the top */}
          <VideoPlayer activeStep={activeStep} steps={steps} />

          {/* Step navigation in a row with gradient borders */}
          <MobileStepNavigation
            steps={steps}
            activeStep={activeStep}
            onStepClick={handleStepClick}
            progress={progress}
          />

          {/* Active step content at the bottom */}
          <MobileStepContent step={steps[activeStep - 1]} />
        </div>
      </div>

      <div className='hidden md:block'>
        {/* Desktop Layout  */}
        <div className='grid items-start gap-12 lg:grid-cols-2 lg:gap-20'>
          {/* Left Side - Steps using StepItem component */}
          <div className='space-y-2 md:space-y-3'>
            {steps.map((step) => (
              <StepItem
                key={step.number}
                step={step}
                isActive={activeStep === step.number}
                isCompleted={activeStep > step.number}
                progress={progress}
                onStepClick={handleStepClick}
              />
            ))}
          </div>

          {/* Right Side - Video using VideoPlayer component */}
          <VideoPlayer activeStep={activeStep} steps={steps} />
        </div>
      </div>

      {/* Play/Pause Control */}
      {/* <div className='mb-8 flex justify-center'>
        <button
          onClick={togglePlayPause}
          className='flex items-center gap-2 rounded-full px-4 py-2 text-white transition-all hover:shadow-lg'
          style={{
            background: `linear-gradient(89.7deg, #62CDFF -43.29%, #7D42A5 17.93%, #FF988A 116.46%, #E4398A 155.68%)`,
          }}
        >
          {isPlaying ? (
            <>
              <PauseIcon className='h-4 w-4' />
              Pause
            </>
          ) : (
            <>
              <PlayIcon className='h-4 w-4' />
              Play
            </>
          )}
        </button>
      </div> */}
    </SectionWrapper>
  )
}
