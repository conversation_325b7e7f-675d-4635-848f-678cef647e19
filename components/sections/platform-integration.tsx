"use client"

import { OrbitingCircles } from "@/components/ui/orbiting-circles"
import { iconGroups } from "@/constants/platform-icons"
import Image from "next/image"

export default function PlatformIntegrationSection() {
  return (
    <section className='relative overflow-hidden bg-primary py-20 text-white'>
      {/* Background Pattern */}
      <div className='absolute inset-0 opacity-5'>
        <div className='absolute left-20 top-10 h-32 w-32 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 blur-2xl'></div>
        <div className='absolute bottom-10 right-20 h-48 w-48 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 blur-2xl'></div>
      </div>

      <div className='container relative z-10 mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='text-center'>
          <h3 className='mb-4 font-afacad text-2xl font-bold text-white md:text-3xl lg:text-4xl'>
            Easy to integrate and supports all platforms
          </h3>

          {/* Platform Icons - Single Orbiting Circle */}
          <div className='relative mx-auto mt-16 flex h-[500px] w-full max-w-[600px] items-center justify-center overflow-hidden'>
            <OrbitingCircles
              iconSize={64}
              radius={200}
              duration={30}
              speed={1.5}
              path={true}
              centerLogo={
                <div className='relative z-[100] rounded-full border border-white/30 bg-white/20 p-6 shadow-2xl backdrop-blur-md'>
                  <Image
                    src='/images/prism-logo.png'
                    alt='Prism Scale Logo'
                    width={100}
                    height={100}
                    className='brightness-110 drop-shadow-2xl filter'
                  />
                </div>
              }
            >
              {/* All platform icons in one circle */}
              {[
                ...iconGroups.outer,
                ...iconGroups.middle,
                ...iconGroups.inner,
              ].map((IconComponent, index) => (
                <IconComponent key={`platform-${index}`} />
              ))}
            </OrbitingCircles>
          </div>

          {/* Additional description */}
          <div className='mx-auto mt-12 max-w-2xl'>
            <p className='text-lg text-gray-400'>
              Seamlessly integrate with your existing e-commerce platform in
              minutes, no technical expertise required.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
