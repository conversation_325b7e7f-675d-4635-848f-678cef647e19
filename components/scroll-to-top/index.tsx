"use client"
import { ChevronUp } from "lucide-react"
import { useEffect, useState } from "react"

export default function ScrollToTop() {
  const [isVisible, setIsVisible] = useState(false)

  // Top: 0 takes us all the way back to the top of the page
  // Behavior: smooth keeps it smooth!
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    })
  }

  useEffect(() => {
    // Button is displayed after scrolling for 500 pixels
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener("scroll", toggleVisibility)

    return () => window.removeEventListener("scroll", toggleVisibility)
  }, [])

  return (
    <div className='z-999 fixed bottom-8 right-8'>
      <div className='flex items-center gap-2.5'>
        {isVisible && (
          <div
            onClick={scrollToTop}
            aria-label='scroll to top'
            className='back-to-top hover:bg-dark flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-black text-white shadow-md transition duration-300 ease-in-out'
          >
            {/* <span className='mt-[6px] h-3 w-3 rotate-45 border-l border-t border-white'></span> */}
            <ChevronUp className='size-4' />
          </div>
        )}
      </div>
    </div>
  )
}
