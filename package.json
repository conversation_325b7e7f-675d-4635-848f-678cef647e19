{"name": "prism-tryon-landing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write ."}, "dependencies": {"@gsap/react": "^2.1.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "@studio-freight/lenis": "^1.0.42", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^10.18.0", "gsap": "^3.13.0", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8.57.1", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^10.1.8", "postcss": "^8", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.3.0", "typescript": "^5"}}